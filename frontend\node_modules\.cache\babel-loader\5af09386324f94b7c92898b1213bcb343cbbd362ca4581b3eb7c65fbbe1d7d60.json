{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Progress, Tag, Space, Typography, message, Spin } from 'antd';\nimport { ProjectOutlined, UserOutlined, BookOutlined, FileTextOutlined, PlusOutlined, EditOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalProjects: 5,\n    totalCharacters: 23,\n    totalVolumes: 12,\n    totalWords: 234567\n  });\n  const [recentProjects, setRecentProjects] = useState([{\n    id: 1,\n    name: '仙侠传说',\n    type: 'xianxia',\n    status: 'writing',\n    progress: 65,\n    lastModified: '2024-01-15',\n    wordCount: 89000\n  }, {\n    id: 2,\n    name: '星际征途',\n    type: 'scifi',\n    status: 'planning',\n    progress: 25,\n    lastModified: '2024-01-14',\n    wordCount: 12000\n  }, {\n    id: 3,\n    name: '都市修仙',\n    type: 'modern',\n    status: 'writing',\n    progress: 80,\n    lastModified: '2024-01-13',\n    wordCount: 156000\n  }]);\n  const [recentActivities, setRecentActivities] = useState([{\n    id: 1,\n    type: 'chapter',\n    action: '创建了新章节',\n    target: '第十二章：突破',\n    project: '仙侠传说',\n    time: '2小时前'\n  }, {\n    id: 2,\n    type: 'character',\n    action: '更新了人物',\n    target: '李逍遥',\n    project: '仙侠传说',\n    time: '4小时前'\n  }, {\n    id: 3,\n    type: 'plot',\n    action: '完成了剧情',\n    target: '主线剧情：初入江湖',\n    project: '仙侠传说',\n    time: '1天前'\n  }, {\n    id: 4,\n    type: 'project',\n    action: '创建了新项目',\n    target: '星际征途',\n    project: '',\n    time: '2天前'\n  }]);\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  const getActivityIcon = type => {\n    const icons = {\n      project: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 16\n      }, this),\n      character: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 18\n      }, this),\n      chapter: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 16\n      }, this),\n      plot: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 13\n      }, this)\n    };\n    return icons[type] || /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 27\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u4EEA\\u8868\\u76D8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        className: \"page-description\",\n        children: \"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF01\\u67E5\\u770B\\u60A8\\u7684\\u521B\\u4F5C\\u6982\\u51B5\\u548C\\u6700\\u8FD1\\u6D3B\\u52A8\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9879\\u76EE\\u603B\\u6570\",\n            value: stats.totalProjects,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EBA\\u7269\\u603B\\u6570\",\n            value: stats.totalCharacters,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5377\\u5B97\\u603B\\u6570\",\n            value: stats.totalVolumes,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: stats.totalWords,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            },\n            formatter: value => `${(value / 10000).toFixed(1)}万`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 14,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u9879\\u76EE\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 23\n            }, this),\n            onClick: () => navigate('/projects'),\n            children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: recentProjects,\n            renderItem: project => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                onClick: () => navigate(`/projects/${project.id}`),\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: project.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: getStatusColor(project.status),\n                    children: getStatusText(project.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    children: getTypeText(project.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  style: {\n                    width: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5B57\\u6570: \", (project.wordCount / 10000).toFixed(1), \"\\u4E07 | \\u6700\\u540E\\u4FEE\\u6539: \", project.lastModified]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: project.progress,\n                    size: \"small\",\n                    status: project.progress === 100 ? 'success' : 'active'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 10,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u6D3B\\u52A8\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: recentActivities,\n            renderItem: activity => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getActivityIcon(activity.type),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: activity.action\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: activity.target\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [activity.project && /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u9879\\u76EE: \", activity.project]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 27\n                    }, this), \" \", activity.time]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/projects'),\n            children: \"\\u521B\\u5EFA\\u65B0\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/characters'),\n            children: \"\\u6DFB\\u52A0\\u4EBA\\u7269\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/volumes'),\n            children: \"\\u7BA1\\u7406\\u5377\\u5B97\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/ai-assistant'),\n            children: \"AI\\u52A9\\u624B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"7AIEpmdUG8gqh7110Vsntt4UfWI=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Progress", "Tag", "Space", "Typography", "message", "Spin", "ProjectOutlined", "UserOutlined", "BookOutlined", "FileTextOutlined", "PlusOutlined", "EditOutlined", "ClockCircleOutlined", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "Dashboard", "_s", "navigate", "stats", "setStats", "totalProjects", "totalCharacters", "totalVolumes", "totalWords", "recentProjects", "setRecentProjects", "id", "name", "type", "status", "progress", "lastModified", "wordCount", "recentActivities", "setRecentActivities", "action", "target", "project", "time", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "getActivityIcon", "icons", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "character", "chapter", "plot", "className", "children", "level", "gutter", "xs", "sm", "lg", "title", "value", "prefix", "valueStyle", "color", "formatter", "toFixed", "style", "marginTop", "extra", "icon", "onClick", "dataSource", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "description", "direction", "width", "percent", "size", "activity", "avatar", "strong", "md", "block", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Progress, Tag, Space, Typography, message, Spin } from 'antd';\nimport {\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard = () => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalProjects: 5,\n    totalCharacters: 23,\n    totalVolumes: 12,\n    totalWords: 234567\n  });\n\n  const [recentProjects, setRecentProjects] = useState([\n    {\n      id: 1,\n      name: '仙侠传说',\n      type: 'xianxia',\n      status: 'writing',\n      progress: 65,\n      lastModified: '2024-01-15',\n      wordCount: 89000\n    },\n    {\n      id: 2,\n      name: '星际征途',\n      type: 'scifi',\n      status: 'planning',\n      progress: 25,\n      lastModified: '2024-01-14',\n      wordCount: 12000\n    },\n    {\n      id: 3,\n      name: '都市修仙',\n      type: 'modern',\n      status: 'writing',\n      progress: 80,\n      lastModified: '2024-01-13',\n      wordCount: 156000\n    }\n  ]);\n\n  const [recentActivities, setRecentActivities] = useState([\n    {\n      id: 1,\n      type: 'chapter',\n      action: '创建了新章节',\n      target: '第十二章：突破',\n      project: '仙侠传说',\n      time: '2小时前'\n    },\n    {\n      id: 2,\n      type: 'character',\n      action: '更新了人物',\n      target: '李逍遥',\n      project: '仙侠传说',\n      time: '4小时前'\n    },\n    {\n      id: 3,\n      type: 'plot',\n      action: '完成了剧情',\n      target: '主线剧情：初入江湖',\n      project: '仙侠传说',\n      time: '1天前'\n    },\n    {\n      id: 4,\n      type: 'project',\n      action: '创建了新项目',\n      target: '星际征途',\n      project: '',\n      time: '2天前'\n    }\n  ]);\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  const getActivityIcon = (type) => {\n    const icons = {\n      project: <ProjectOutlined />,\n      character: <UserOutlined />,\n      chapter: <FileTextOutlined />,\n      plot: <BookOutlined />\n    };\n    return icons[type] || <EditOutlined />;\n  };\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">仪表盘</Title>\n        <Text className=\"page-description\">欢迎回来！查看您的创作概况和最近活动。</Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} className=\"stats-grid\">\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"项目总数\"\n              value={stats.totalProjects}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"人物总数\"\n              value={stats.totalCharacters}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"卷宗总数\"\n              value={stats.totalVolumes}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={stats.totalWords}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n              formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        {/* 最近项目 */}\n        <Col xs={24} lg={14}>\n          <Card\n            title=\"最近项目\"\n            extra={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => navigate('/projects')}\n              >\n                新建项目\n              </Button>\n            }\n          >\n            <List\n              dataSource={recentProjects}\n              renderItem={(project) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      onClick={() => navigate(`/projects/${project.id}`)}\n                    >\n                      查看详情\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        <span>{project.name}</span>\n                        <Tag color={getStatusColor(project.status)}>\n                          {getStatusText(project.status)}\n                        </Tag>\n                        <Tag>{getTypeText(project.type)}</Tag>\n                      </Space>\n                    }\n                    description={\n                      <Space direction=\"vertical\" style={{ width: '100%' }}>\n                        <div>\n                          <Text type=\"secondary\">\n                            字数: {(project.wordCount / 10000).toFixed(1)}万 |\n                            最后修改: {project.lastModified}\n                          </Text>\n                        </div>\n                        <Progress\n                          percent={project.progress}\n                          size=\"small\"\n                          status={project.progress === 100 ? 'success' : 'active'}\n                        />\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 最近活动 */}\n        <Col xs={24} lg={10}>\n          <Card title=\"最近活动\">\n            <List\n              dataSource={recentActivities}\n              renderItem={(activity) => (\n                <List.Item>\n                  <List.Item.Meta\n                    avatar={getActivityIcon(activity.type)}\n                    title={\n                      <Space>\n                        <span>{activity.action}</span>\n                        <Text strong>{activity.target}</Text>\n                      </Space>\n                    }\n                    description={\n                      <Space>\n                        {activity.project && (\n                          <Text type=\"secondary\">项目: {activity.project}</Text>\n                        )}\n                        <Text type=\"secondary\">\n                          <ClockCircleOutlined /> {activity.time}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快速操作 */}\n      <Card title=\"快速操作\" style={{ marginTop: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<ProjectOutlined />}\n              onClick={() => navigate('/projects')}\n            >\n              创建新项目\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<UserOutlined />}\n              onClick={() => navigate('/characters')}\n            >\n              添加人物\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<FileTextOutlined />}\n              onClick={() => navigate('/volumes')}\n            >\n              管理卷宗\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<BookOutlined />}\n              onClick={() => navigate('/ai-assistant')}\n            >\n              AI助手\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AAC/G,SACEC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,mBAAmB,QACd,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGf,UAAU;AAElC,MAAMgB,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC;IACjCgC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,CACnD;IACEsC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,YAAY;IAC1BC,SAAS,EAAE;EACb,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,UAAU;IAClBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,YAAY;IAC1BC,SAAS,EAAE;EACb,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,YAAY;IAC1BC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,CACvD;IACEsC,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,SAAS;IACfO,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE;EACR,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,WAAW;IACjBO,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE;EACR,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,MAAM;IACZO,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE;EACR,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,SAAS;IACfO,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EAEF,MAAMC,cAAc,GAAIV,MAAM,IAAK;IACjC,MAAMW,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACX,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMiB,aAAa,GAAIjB,MAAM,IAAK;IAChC,MAAMkB,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAAClB,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMmB,WAAW,GAAIpB,IAAI,IAAK;IAC5B,MAAMmB,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAACnB,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAM4B,eAAe,GAAI5B,IAAI,IAAK;IAChC,MAAM6B,KAAK,GAAG;MACZpB,OAAO,eAAEzB,OAAA,CAACV,eAAe;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5BC,SAAS,eAAElD,OAAA,CAACT,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3BE,OAAO,eAAEnD,OAAA,CAACP,gBAAgB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC7BG,IAAI,eAAEpD,OAAA,CAACR,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACvB,CAAC;IACD,OAAOJ,KAAK,CAAC7B,IAAI,CAAC,iBAAIhB,OAAA,CAACL,YAAY;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC,CAAC;EAED,oBACEjD,OAAA;IAAKqD,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBtD,OAAA;MAAKqD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtD,OAAA,CAACC,KAAK;QAACsD,KAAK,EAAE,CAAE;QAACF,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAG;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDjD,OAAA,CAACE,IAAI;QAACmD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAmB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGNjD,OAAA,CAACrB,GAAG;MAAC6E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACH,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC3CtD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACzBtD,OAAA,CAACtB,IAAI;UAAA4E,QAAA,eACHtD,OAAA,CAACnB,SAAS;YACR+E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEvD,KAAK,CAACE,aAAc;YAC3BsD,MAAM,eAAE9D,OAAA,CAACV,eAAe;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5Bc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACzBtD,OAAA,CAACtB,IAAI;UAAA4E,QAAA,eACHtD,OAAA,CAACnB,SAAS;YACR+E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEvD,KAAK,CAACG,eAAgB;YAC7BqD,MAAM,eAAE9D,OAAA,CAACT,YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACzBtD,OAAA,CAACtB,IAAI;UAAA4E,QAAA,eACHtD,OAAA,CAACnB,SAAS;YACR+E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEvD,KAAK,CAACI,YAAa;YAC1BoD,MAAM,eAAE9D,OAAA,CAACP,gBAAgB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7Bc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACzBtD,OAAA,CAACtB,IAAI;UAAA4E,QAAA,eACHtD,OAAA,CAACnB,SAAS;YACR+E,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAEvD,KAAK,CAACK,UAAW;YACxBmD,MAAM,eAAE9D,OAAA,CAACR,YAAY;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBc,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjCC,SAAS,EAAGJ,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEK,OAAO,CAAC,CAAC,CAAC;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjD,OAAA,CAACrB,GAAG;MAAC6E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACW,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAd,QAAA,gBAE9CtD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAL,QAAA,eAClBtD,OAAA,CAACtB,IAAI;UACHkF,KAAK,EAAC,0BAAM;UACZS,KAAK,eACHrE,OAAA,CAACjB,MAAM;YACLiC,IAAI,EAAC,SAAS;YACdsD,IAAI,eAAEtE,OAAA,CAACN,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,WAAW,CAAE;YAAAiD,QAAA,EACtC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAK,QAAA,eAEDtD,OAAA,CAAClB,IAAI;YACH0F,UAAU,EAAE5D,cAAe;YAC3B6D,UAAU,EAAGhD,OAAO,iBAClBzB,OAAA,CAAClB,IAAI,CAAC4F,IAAI;cACRC,OAAO,EAAE,cACP3E,OAAA,CAACjB,MAAM;gBACLiC,IAAI,EAAC,MAAM;gBACXuD,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,aAAaoB,OAAO,CAACX,EAAE,EAAE,CAAE;gBAAAwC,QAAA,EACpD;cAED;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAK,QAAA,eAEFtD,OAAA,CAAClB,IAAI,CAAC4F,IAAI,CAACE,IAAI;gBACbhB,KAAK,eACH5D,OAAA,CAACd,KAAK;kBAAAoE,QAAA,gBACJtD,OAAA;oBAAAsD,QAAA,EAAO7B,OAAO,CAACV;kBAAI;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BjD,OAAA,CAACf,GAAG;oBAAC+E,KAAK,EAAErC,cAAc,CAACF,OAAO,CAACR,MAAM,CAAE;oBAAAqC,QAAA,EACxCpB,aAAa,CAACT,OAAO,CAACR,MAAM;kBAAC;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNjD,OAAA,CAACf,GAAG;oBAAAqE,QAAA,EAAElB,WAAW,CAACX,OAAO,CAACT,IAAI;kBAAC;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CACR;gBACD4B,WAAW,eACT7E,OAAA,CAACd,KAAK;kBAAC4F,SAAS,EAAC,UAAU;kBAACX,KAAK,EAAE;oBAAEY,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,gBACnDtD,OAAA;oBAAAsD,QAAA,eACEtD,OAAA,CAACE,IAAI;sBAACc,IAAI,EAAC,WAAW;sBAAAsC,QAAA,GAAC,gBACjB,EAAC,CAAC7B,OAAO,CAACL,SAAS,GAAG,KAAK,EAAE8C,OAAO,CAAC,CAAC,CAAC,EAAC,qCACtC,EAACzC,OAAO,CAACN,YAAY;oBAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjD,OAAA,CAAChB,QAAQ;oBACPgG,OAAO,EAAEvD,OAAO,CAACP,QAAS;oBAC1B+D,IAAI,EAAC,OAAO;oBACZhE,MAAM,EAAEQ,OAAO,CAACP,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;kBAAS;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjD,OAAA,CAACpB,GAAG;QAAC6E,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAL,QAAA,eAClBtD,OAAA,CAACtB,IAAI;UAACkF,KAAK,EAAC,0BAAM;UAAAN,QAAA,eAChBtD,OAAA,CAAClB,IAAI;YACH0F,UAAU,EAAEnD,gBAAiB;YAC7BoD,UAAU,EAAGS,QAAQ,iBACnBlF,OAAA,CAAClB,IAAI,CAAC4F,IAAI;cAAApB,QAAA,eACRtD,OAAA,CAAClB,IAAI,CAAC4F,IAAI,CAACE,IAAI;gBACbO,MAAM,EAAEvC,eAAe,CAACsC,QAAQ,CAAClE,IAAI,CAAE;gBACvC4C,KAAK,eACH5D,OAAA,CAACd,KAAK;kBAAAoE,QAAA,gBACJtD,OAAA;oBAAAsD,QAAA,EAAO4B,QAAQ,CAAC3D;kBAAM;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BjD,OAAA,CAACE,IAAI;oBAACkF,MAAM;oBAAA9B,QAAA,EAAE4B,QAAQ,CAAC1D;kBAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACR;gBACD4B,WAAW,eACT7E,OAAA,CAACd,KAAK;kBAAAoE,QAAA,GACH4B,QAAQ,CAACzD,OAAO,iBACfzB,OAAA,CAACE,IAAI;oBAACc,IAAI,EAAC,WAAW;oBAAAsC,QAAA,GAAC,gBAAI,EAAC4B,QAAQ,CAACzD,OAAO;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACpD,eACDjD,OAAA,CAACE,IAAI;oBAACc,IAAI,EAAC,WAAW;oBAAAsC,QAAA,gBACpBtD,OAAA,CAACJ,mBAAmB;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAACiC,QAAQ,CAACxD,IAAI;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjD,OAAA,CAACtB,IAAI;MAACkF,KAAK,EAAC,0BAAM;MAACO,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAd,QAAA,eAC1CtD,OAAA,CAACrB,GAAG;QAAC6E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAF,QAAA,gBACpBtD,OAAA,CAACpB,GAAG;UAAC6E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2B,EAAE,EAAE,CAAE;UAAA/B,QAAA,eACzBtD,OAAA,CAACjB,MAAM;YACLiC,IAAI,EAAC,QAAQ;YACbsE,KAAK;YACLL,IAAI,EAAC,OAAO;YACZX,IAAI,eAAEtE,OAAA,CAACV,eAAe;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BsB,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,WAAW,CAAE;YAAAiD,QAAA,EACtC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjD,OAAA,CAACpB,GAAG;UAAC6E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2B,EAAE,EAAE,CAAE;UAAA/B,QAAA,eACzBtD,OAAA,CAACjB,MAAM;YACLiC,IAAI,EAAC,QAAQ;YACbsE,KAAK;YACLL,IAAI,EAAC,OAAO;YACZX,IAAI,eAAEtE,OAAA,CAACT,YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,aAAa,CAAE;YAAAiD,QAAA,EACxC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjD,OAAA,CAACpB,GAAG;UAAC6E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2B,EAAE,EAAE,CAAE;UAAA/B,QAAA,eACzBtD,OAAA,CAACjB,MAAM;YACLiC,IAAI,EAAC,QAAQ;YACbsE,KAAK;YACLL,IAAI,EAAC,OAAO;YACZX,IAAI,eAAEtE,OAAA,CAACP,gBAAgB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BsB,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,UAAU,CAAE;YAAAiD,QAAA,EACrC;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjD,OAAA,CAACpB,GAAG;UAAC6E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAC2B,EAAE,EAAE,CAAE;UAAA/B,QAAA,eACzBtD,OAAA,CAACjB,MAAM;YACLiC,IAAI,EAAC,QAAQ;YACbsE,KAAK;YACLL,IAAI,EAAC,OAAO;YACZX,IAAI,eAAEtE,OAAA,CAACR,YAAY;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEA,CAAA,KAAMlE,QAAQ,CAAC,eAAe,CAAE;YAAAiD,QAAA,EAC1C;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7C,EAAA,CA3TID,SAAS;EAAA,QACIN,WAAW;AAAA;AAAA0F,EAAA,GADxBpF,SAAS;AA6Tf,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}