# NovelCraft - 小说管理系统

一个全方位的小说创作与管理系统，集成智能AI助手，支持从创意构思到完整小说的全流程创作。

## 🚀 快速启动

### 启动方式

**推荐使用以下启动脚本：**

1. **Start-System.bat** - 一键启动脚本（推荐）
2. **Stop-System.bat** - 一键停止脚本
3. **Install-Dependencies.bat** - 依赖安装脚本

### 启动步骤

1. 双击运行 `Start-System.bat`
2. 等待后端和前端服务启动
3. 浏览器会自动打开前端界面 (http://localhost:3000)
4. API文档地址: http://localhost:8000/docs

### 停止服务

- 双击运行 `Stop-System.bat` 停止所有服务
- 或者在启动窗口中按 `Ctrl+C` 停止服务

### 系统要求

#### 环境依赖
- **Python**: 3.9或更高版本
- **Node.js**: 16或更高版本
- **操作系统**: Windows 10/11（推荐）

#### 快速安装
1. 双击运行 `Install-Dependencies.bat` - 自动安装所有依赖
2. 双击运行 `Start-System.bat` - 启动NovelCraft系统
3. 双击运行 `Stop-System.bat` - 停止所有服务

### 启动流程详解

#### 第一次使用
1. **安装依赖**: 双击 `Install-Dependencies.bat`
   - 自动检查Python和Node.js环境
   - 安装后端Python依赖包
   - 安装前端Node.js依赖包

2. **启动系统**: 双击 `Start-System.bat`
   - 自动检查环境和依赖
   - 启动后端API服务（端口8000）
   - 启动前端界面服务（端口3000）
   - 自动打开浏览器访问系统

3. **停止系统**: 双击 `Stop-System.bat`
   - 安全停止所有相关进程
   - 清理临时文件和窗口

#### 日常使用
- **启动**: 双击 `Start-System.bat`
- **停止**: 双击 `Stop-System.bat`

### 故障排除

#### 常见问题
1. **Python环境问题**: 确保已安装Python 3.9+并添加到PATH
2. **Node.js环境问题**: 确保已安装Node.js 16+并添加到PATH
3. **端口占用**: 确保8000和3000端口未被其他程序占用
4. **网络问题**: 依赖安装需要网络连接，请检查网络设置

#### 解决方案
- 重新运行 `Install-Dependencies.bat` 修复依赖问题
- 运行 `Stop-System.bat` 清理进程后重新启动
- 检查防火墙和杀毒软件设置

### Ollama问题排查

#### 快速检查
- 双击运行 `Check-Ollama.bat` - 自动检查Ollama状态
- 运行 `python ollama_diagnostic.py` - 详细诊断
- 运行 `python fix_ollama_issues.py` - 自动修复

#### 常见问题
1. **模型检测失败**: Ollama服务未启动
   - 解决: 运行 `ollama serve` 启动服务
2. **连接超时**: 防火墙阻止端口11434
   - 解决: 检查防火墙设置
3. **默认模型缺失**: 未下载mollysama/rwkv-7-g1:0.4B
   - 解决: 运行 `ollama pull mollysama/rwkv-7-g1:0.4B`

## 📖 项目概述

NovelCraft是一个全方位的小说创作与管理系统，旨在帮助作者从多个维度管理小说设定、剧情发展、人物关系等内容，并通过AI辅助生成、分析和续写功能，确保故事的连贯性和完整性。

### 🎯 核心特色

- **🤖 智能AI助手**：集成多种AI模型，支持智能对话创作
- **📊 项目级数据管理**：完整的数据隔离和权限管理
- **🎨 可视化设计**：关系图谱、时间线等可视化工具
- **🔧 高度可扩展**：模块化设计，支持自定义扩展
- **🛡️ 数据安全**：本地优先，支持云端备份

### 🏗️ 系统架构

#### 导航结构
```
仪表盘
│
项目管理
│
├─ 卷宗管理（独立页面）
├─ 内容管理（独立页面）
├─ 设定管理（独立页面）
│
工具
├─ Agent-AI助手
├─ Agent-AI测试
└─ 系统设置
```

#### 技术栈
- **后端**: Python 3.9+ + FastAPI + SQLite/MongoDB
- **前端**: React + Electron + Ant Design
- **AI引擎**: 基于大型语言模型的文本生成与分析
- **可视化**: D3.js + Timeline.js + Leaflet.js

#### 核心模块
1. **项目管理模块** - 项目概览、统计数据、进度跟踪
2. **卷宗管理模块** - 卷宗结构、章节管理、导出发布
3. **内容管理模块** - 人物势力、资源分布、关系网络
4. **设定管理模块** - 世界观、体系设定、规则管理
5. **AI助手模块** - 智能对话、内容生成、协作引擎
6. **工具模块** - AI测试、系统设置、配置管理

## 项目结构

```
NovelCraft/
├── backend/                    # 后端服务
│   ├── app/                   # 应用主目录
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   ├── utils/            # 工具函数
│   │   └── main.py           # 应用入口
│   ├── tests/                # 测试文件
│   ├── requirements.txt      # Python依赖
│   └── alembic/              # 数据库迁移
├── frontend/                  # 前端应用
│   ├── src/                  # 源代码
│   │   ├── components/       # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   ├── styles/          # 样式文件
│   │   └── App.js           # 应用入口
│   ├── public/              # 静态资源
│   ├── package.json         # 前端依赖
│   └── electron.js          # Electron主进程
├── ai_engine/                # AI引擎
│   ├── generators/          # 内容生成器
│   ├── analyzers/           # 内容分析器
│   ├── models/              # AI模型
│   └── utils/               # AI工具函数
├── docs/                     # 文档
├── scripts/                  # 构建脚本
└── README.md                # 项目说明
```

## 数据模型

### 核心实体
- **Project**: 项目基本信息
- **WorldSetting**: 世界观设定
- **CultivationSystem**: 修炼体系
- **Character**: 人物信息
- **Faction**: 势力组织
- **Plot**: 剧情内容
- **Chapter**: 章节内容
- **Timeline**: 时间线事件

### 关系模型
- **CharacterRelation**: 人物关系
- **FactionRelation**: 势力关系
- **EventAssociation**: 事件关联

## 功能特性

### ✅ 已实现功能

#### 🏗️ 基础架构
- [x] 项目基础架构搭建
- [x] 数据模型设计完成
- [x] API接口框架建立
- [x] 用户界面框架搭建

#### 📊 项目管理
- [x] 项目管理功能完善
- [x] 仪表盘界面
- [x] 项目列表和详情页面
- [x] **🆕 预置项目功能** - 提供完整的项目模板，支持复制但不允许编辑删除
- [x] **🆕 项目级数据管理** - 项目数据隔离、统一访问接口、AI数据权限管理

#### 📝 内容管理
- [x] **人物管理功能** - 完整的人物档案管理系统
- [x] **势力管理功能** - 势力组织、关系网络、等级体系
- [x] **剧情管理功能** - 剧情线索、发展跟踪、冲突设定
- [x] **章节管理功能** - 章节列表、编辑、状态跟踪
- [x] **关系网络功能** - 人物关系图谱、关系类型分类
- [x] **时间线功能** - 事件时间轴、重要节点标记

#### ⚙️ 设定管理
- [x] **世界设定管理** - 世界观、地理、历史、文化设定
- [x] **修炼体系功能** - 修炼等级、方法配置、境界设定
- [x] **政治体系管理** - 政府结构、法律体系、权力分配
- [x] **货币体系管理** - 货币制度、金融体系、经济指标
- [x] **商业体系管理** - 贸易体系、商业组织、市场机制
- [x] **种族体系管理** - 种族分类、能力特征、文化社会
- [x] **功法体系管理** - 功法分类、修炼体系、技能招式
- [x] **装备体系管理** - 装备分类、强化系统、制作信息
- [x] **宠物体系管理** - 宠物分类、培养系统、进化链
- [x] **地图结构管理** - 地理信息、地形特征、人文地理
- [x] **维度结构管理** - 维度属性、空间结构、连接信息
- [x] **灵宝体系管理** - 灵宝属性、炼制信息、器灵系统
- [x] **生民体系管理** - 人口统计、社会阶层、生活方式
- [x] **司法体系管理** - 法院体系、执法机构、法律条文
- [x] **职业体系管理** - 职业分类、技能体系、行业组织

#### 🤖 AI助手功能
- [x] **AI多平台支持** - OpenAI、Claude、智谱AI、硅基流动、谷歌AI、GROK3AI、Ollama
- [x] **AI助手界面** - 智能对话、内容生成、设定创建
- [x] **系统设置管理** - AI配置、通用设置、数据管理
- [x] **AI服务抽象层** - 统一的AI调用接口
- [x] **🆕 Ollama模型自动检测** - 实时检测本地模型、动态选择界面
- [x] **🆕 智能对话引擎** - AI助手智能对话、项目创建引导、设定收集

#### 🚧 待实现功能
- [ ] 智能对话创作系统完善
- [ ] AI协作工作流引擎
- [ ] 章节富文本编辑器
- [ ] 数据导入导出功能
- [ ] 关系网络可视化图表
- [ ] 时间线可视化图表

### 🎯 核心功能

#### 1. 项目管理
- **项目生命周期管理**: 创建、编辑、删除、导入导出小说项目
- **项目概览**: 统计数据、进度跟踪、快速访问入口
- **预置项目模板**: 玄幻、都市、科幻等完整项目模板
- **数据隔离**: 项目级数据管理，确保数据安全和独立性

#### 2. 卷宗管理
- **卷宗结构管理**: 卷宗的创建、编辑、删除和层级管理
- **章节管理**: 在对应卷宗下管理章节内容、大纲、摘要
- **独立章节管理**: 管理未分配到卷宗的独立章节
- **导出与发布**: 多格式导出（TXT、DOCX、PDF、EPUB）、平台发布

#### 3. 内容管理
- **人物管理**: 人物档案、关系网络、成长轨迹追踪
- **势力管理**: 势力组织、关系网络、等级体系
- **剧情管理**: 主线支线管理、剧情线索、冲突设定
- **资源分布**: 各种资源在世界中的地理分布、开采难度、经济价值
- **种族分布**: 种族在地理上的分布情况、人口统计、势力范围
- **秘境分布**: 秘境、副本、特殊区域的分布、危险等级、奖励机制
- **关系网络**: 人物、势力间的关系可视化

#### 4. 设定管理
- **世界观与基础设定**: 世界设定、地图结构、维度结构、种族类别
- **力量与战斗体系**: 修炼体系、功法体系、装备体系、灵宝体系、宠物体系
- **社会与政治体系**: 政治体系、司法体系、生民体系、职业体系
- **经济与商业体系**: 货币体系、商业体系

#### 5. AI助手功能
- **智能对话创作**: 通过多轮对话收集创作需求，自动生成设定
- **内容生成**: 设定生成、剧情分析、续写建议、一致性检查
- **AI协作引擎**: 多AI助手协同工作，支持编剧AI、写作AI、总结AI、读者AI
- **项目数据权限**: AI助手对整个项目具备完整的可控、可读写权限

#### 6. 工具模块
- **Agent-AI助手**: 智能对话、内容生成、项目数据集成
- **Agent-AI测试**: AI功能测试、质量评估、调试诊断
- **系统设置**: AI配置管理、系统通用设置、性能优化

### 🤖 AI功能特性

#### 🌐 多平台AI支持
- **OpenAI**: 支持GPT-3.5、GPT-4等模型
- **Claude**: 支持Claude 3 Haiku、Sonnet、Opus
- **智谱AI**: 支持GLM-4、GLM-3 Turbo、ChatGLM3-6B
- **硅基流动**: 支持DeepSeek-Chat、Qwen-Turbo、Yi-Large
- **谷歌AI**: 支持Gemini-Pro、Gemini-Pro-Vision、Gemini-Ultra
- **GROK3AI**: 支持Grok-Beta、Grok-1
- **Ollama**: 支持本地部署的开源模型（默认配置）
- **自定义接口**: 兼容OpenAI API格式的自定义服务

#### 🧠 智能对话创作系统
- **多轮对话引擎**: 通过智能对话收集小说主题、世界设定等信息
- **意图识别分析**: 智能分析用户创作意图和偏好
- **渐进式引导**: 从宏观到微观，逐步构建完整的小说基础设施
- **阶段管理**: 主题确定→世界设定→体系构建→人物角色→势力分布→时间线→剧情架构
- **自动数据写入**: 对话过程中自动分类保存到对应数据管理页面
- **上下文记忆**: 保持对话连贯性，理解用户的创作偏好和风格

#### 🤝 AI协作引擎
- **编剧AI**: 根据命题生成大纲、设定、主线剧情
- **写作AI**: 结合设定和大纲，分章节生成详细剧情文本
- **总结AI**: 对章节、卷进行总结，生成前言和关键信息
- **读者AI**: 模拟读者评价，提出正负反馈和改进建议
- **设定管理AI**: 智能管理和维护所有设定信息，确保一致性
- **协作反馈机制**: 各AI模块自动传递内容，形成闭环反馈

#### 🎨 智能内容生成
- **世界设定生成**: 根据关键词生成详细的世界观设定
- **人物角色生成**: 创建具有完整背景的角色档案
- **剧情大纲生成**: 生成结构化的故事情节
- **章节续写**: 基于已有内容智能续写
- **一致性检查**: 检测内容中的逻辑矛盾
- **批量生成模式**: 支持批量生成多种方案供用户选择

#### 🔧 灵活配置与管理
- **动态切换**: 实时切换不同AI提供商
- **参数调节**: 自定义温度、Token数等参数
- **状态监控**: 实时显示AI服务连接状态
- **错误处理**: 完善的异常处理和重试机制
- **思维链处理**: AI推理过程可视化，支持思维链分离显示
- **模型自动检测**: Ollama模型自动检测和动态选择

## AI配置说明

### 支持的AI平台

#### 1. OpenAI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: GPT-3.5 Turbo, GPT-4, GPT-4 Turbo
- **获取方式**: 访问 [OpenAI官网](https://platform.openai.com/) 获取API Key

#### 2. Claude (Anthropic)
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Claude 3 Haiku, Claude 3 Sonnet, Claude 3 Opus
- **获取方式**: 访问 [Anthropic官网](https://console.anthropic.com/) 获取API Key

#### 3. 智谱AI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: GLM-4, GLM-3 Turbo, ChatGLM3-6B
- **获取方式**: 访问 [智谱AI官网](https://open.bigmodel.cn/) 获取API Key

#### 4. 硅基流动
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: DeepSeek-Chat, Qwen-Turbo, Yi-Large
- **获取方式**: 访问 [硅基流动官网](https://siliconflow.cn/) 获取API Key

#### 5. Google AI
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Gemini-Pro, Gemini-Pro-Vision, Gemini-Ultra
- **获取方式**: 访问 [Google AI Studio](https://makersuite.google.com/) 获取API Key

#### 6. Grok (xAI)
- **配置项**: API Key, Base URL, 模型名称
- **支持模型**: Grok-Beta, Grok-1
- **获取方式**: 访问 [xAI官网](https://x.ai/) 获取API Key

#### 7. Ollama (本地部署) 🆕 **[默认配置]**
- **配置项**: 服务地址, 模型名称
- **默认设置**:
  - **默认服务地址**: http://localhost:11434
  - **默认模型**: mollysama/rwkv-7-g1:0.4B
- **支持模型**: Llama2, Mistral, CodeLlama, RWKV等开源模型
- **🔥 新功能**: **自动模型检测** - 系统会自动检测本地已安装的Ollama模型
- **安装方式**:
  1. 下载安装 [Ollama](https://ollama.ai/)
  2. 运行 `ollama pull mollysama/rwkv-7-g1:0.4B` 下载默认模型
  3. 启动服务 `ollama serve`
- **智能特性**:
  - 🔍 实时检测本地可用模型
  - 📊 显示模型大小和详细信息
  - 🔄 一键刷新模型列表
  - ⚡ 动态模型选择界面

#### 8. 自定义OpenAI兼容接口
- **配置项**: API Key, Base URL, 模型名称
- **适用场景**: 支持OpenAI API格式的第三方服务
- **示例**: OneAPI, FastChat, vLLM等

### 配置步骤
1. 启动应用后，访问 **系统设置** → **AI配置**
2. 选择要使用的AI提供商
3. 填入相应的配置信息（API Key、服务地址等）
4. 点击 **测试连接** 验证配置
5. 保存设置并切换到对应提供商

## 安装与运行

### 一键安装和启动（推荐）

#### 完整安装流程
1. **安装依赖**: 双击 `Install-Dependencies.bat`
   - 自动检查Python和Node.js环境
   - 安装所有必需的依赖包
   - 显示详细的安装进度

2. **启动系统**: 双击 `Start-System.bat`
   - 自动环境检查和依赖验证
   - 启动后端和前端服务
   - 自动打开浏览器

3. **停止系统**: 双击 `Stop-System.bat`
   - 安全停止所有服务
   - 清理相关进程

### 手动安装（开发者）

#### 后端安装
```bash
cd backend
pip install -r requirements.txt
```

#### 前端安装
```bash
cd frontend
npm install
```

### 手动启动服务

#### 启动后端
```bash
cd backend
python run.py
```

#### 启动前端
```bash
cd frontend
npm start
```

#### Electron桌面应用
```bash
cd frontend
npm run electron-dev
```

### 访问地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

### 服务管理

#### 启动服务
- 双击 `Start-System.bat` - 完整启动流程
- 或手动启动后端和前端服务

#### 停止服务
- 双击 `Stop-System.bat` - 安全停止所有服务
- 或在启动窗口按 `Ctrl+C` 停止

#### 重新安装依赖
- 双击 `Install-Dependencies.bat` - 重新安装所有依赖

## 开发指南

### 代码规范
- Python: 遵循PEP 8规范
- JavaScript: 使用ESLint + Prettier
- 组件命名: PascalCase
- 文件命名: kebab-case

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 使用说明

### 基本操作流程

1. **创建项目**
   - 在项目管理页面点击"新建项目"
   - 填写项目基本信息（名称、类型、简介等）
   - 选择合适的项目模板（可选）

2. **设定管理**
   - 创建世界观设定（地理、历史、文化等）
   - 设计修炼体系（等级、能力、规则等）
   - 建立政治经济系统

3. **人物管理**
   - 创建主要角色和配角
   - 设定人物属性、性格、背景
   - 建立人物关系网络

4. **剧情规划**
   - 设计主线和支线剧情
   - 创建章节大纲
   - 安排时间线和事件

5. **内容创作**
   - 使用章节编辑器写作
   - 利用AI助手辅助创作
   - 进行一致性检查

### 主要功能模块

#### 1. 项目仪表盘
- 项目概览和统计信息
- 最近活动和进度跟踪
- 快速操作入口

#### 2. 项目管理
- 项目创建、编辑、删除
- 项目复制和模板功能
- 项目导入导出
- **🆕 预置项目模板**:
  - 玄幻世界模板 - 完整的修仙世界设定
  - 现代都市模板 - 都市异能世界设定
  - 科幻星际模板 - 星际文明设定
  - 预置项目只能查看和复制，不能编辑或删除

#### 3. 内容管理
- 人物档案管理
- 势力组织管理
- 剧情线索管理
- 章节内容管理

#### 4. 设定管理
- 世界观设定
- 修炼体系设计
- 时间线管理
- 关系网络可视化

#### 5. AI辅助工具
- 智能设定生成
- 剧情分析建议
- 内容续写辅助
- 一致性自动检查

#### 6. 项目级数据管理 🆕
- **数据隔离**: 每个项目的数据完全独立，确保项目间不会相互影响
- **统一访问**: 提供统一的项目数据访问接口，支持所有设定管理模块
- **AI数据权限**: AI助手可以读写指定项目的全部数据，支持智能创作
- **批量操作**: 支持项目数据的批量创建、更新、删除和复制
- **数据验证**: 自动验证项目数据的完整性和一致性
- **操作日志**: 记录AI助手的所有数据操作，便于追踪和调试

#### 7. 智能对话引擎 🆕
- **多轮对话**: 支持与AI助手进行连续的智能对话
- **项目引导**: 通过对话引导用户完成项目创建和设定管理
- **意图识别**: 智能分析用户输入，提取关键信息和创作意图
- **阶段管理**: 按照创作流程分阶段收集和整理项目信息
- **自动保存**: 对话过程中自动保存收集的设定信息到项目数据库
- **上下文记忆**: 保持对话连贯性，理解用户的创作偏好和风格

### 技术特性

- **跨平台支持**: 基于Electron的桌面应用
- **实时保存**: 自动保存和版本控制
- **数据安全**: 本地存储和云端备份
- **可视化**: 关系图谱和时间线展示
- **AI集成**: 智能创作辅助功能

## 🗺️ 开发路线图

### v1.0 (当前版本) ✅
#### 🏗️ 基础架构
- [x] 基础架构搭建完成
- [x] 用户界面框架建立
- [x] API接口框架完善
- [x] 数据模型设计完成

#### 📊 项目管理
- [x] 项目管理功能完善
- [x] 🆕 预置项目功能 - 完整的项目模板系统
- [x] 🆕 项目级数据管理 - 数据隔离和AI数据权限管理

#### 📝 内容与设定管理
- [x] 人物管理系统完善
- [x] 势力管理系统
- [x] 剧情管理系统
- [x] 章节管理功能
- [x] 世界设定管理
- [x] 修炼体系设计器
- [x] 关系网络管理
- [x] 时间线管理
- [x] 完整的设定管理体系（政治、货币、商业、种族、功法、装备、宠物、地图、维度、灵宝、生民、司法、职业）

#### 🤖 AI助手功能
- [x] 多平台AI支持（OpenAI、Claude、智谱AI、硅基流动、谷歌AI、GROK3AI、Ollama）
- [x] 🆕 Ollama模型自动检测功能
- [x] 🆕 智能对话引擎 - AI助手智能对话和项目创建引导
- [x] AI服务抽象层
- [x] 基础AI功能

### v1.1 (开发中) 🚧
#### 🧠 智能对话系统完善
- [ ] 完整的智能对话创作流程
- [ ] AI协作工作流引擎
- [ ] 思维链处理优化
- [ ] 批量生成模式

#### 🎨 用户体验优化
- [ ] 富文本章节编辑器
- [ ] 关系网络可视化图表
- [ ] 时间线可视化图表
- [ ] 内容管理和设定管理可折叠功能

#### 📤 数据管理
- [ ] 数据导入导出功能
- [ ] 项目备份与恢复
- [ ] 数据版本控制

### v1.2 (计划中) 📋
#### 🤖 高级AI功能
- [ ] 编剧AI、写作AI、总结AI、读者AI协作系统
- [ ] AI助手智能测试系统
- [ ] 内容质量评估系统
- [ ] 自动化创作流程

#### 🌐 扩展功能
- [ ] 多语言支持
- [ ] 插件系统
- [ ] 云端同步
- [ ] 协作功能

### v2.0 (远期规划) 🚀
#### 📱 跨平台支持
- [ ] 移动端应用
- [ ] Web版本
- [ ] 云端部署

#### 🏢 专业功能
- [ ] 发布平台集成
- [ ] 专业版功能
- [ ] 团队协作
- [ ] 版权管理

## API使用说明 🆕

### 项目数据管理API

#### 基础数据操作
```bash
# 获取项目所有数据
GET /project-data/projects/{project_id}/data

# 获取指定类型数据
GET /project-data/projects/{project_id}/data?model_name=character

# 创建项目数据
POST /project-data/projects/{project_id}/data/{model_name}
{
  "data": {
    "name": "角色名称",
    "description": "角色描述"
  }
}

# 更新项目数据
PUT /project-data/projects/{project_id}/data/{model_name}/{item_id}
{
  "data": {
    "name": "更新后的名称"
  }
}

# 删除项目数据
DELETE /project-data/projects/{project_id}/data/{model_name}/{item_id}
```

#### AI助手专用接口
```bash
# 设置AI当前操作项目
POST /project-data/ai/set-project/{project_id}

# AI读取项目数据
GET /project-data/ai/read-data?data_type=character

# AI写入项目数据
POST /project-data/ai/write-data/{data_type}?operation=create
{
  "data": {
    "name": "AI生成的角色",
    "attributes": {...}
  }
}

# AI批量写入数据
POST /project-data/ai/batch-write
{
  "batch_data": {
    "character": [...],
    "world_setting": [...]
  }
}

# AI搜索项目数据
POST /project-data/ai/search
{
  "query": "搜索关键词",
  "data_types": ["character", "faction"]
}

# 获取项目上下文
GET /project-data/ai/context

# 获取AI操作日志
GET /project-data/ai/operation-log?limit=50
```

### 智能对话API

```bash
# 开始智能对话
POST /conversation/start
{
  "user_id": "user123",
  "project_id": 1  # 可选，用于现有项目
}

# 处理用户输入
POST /conversation/input
{
  "user_id": "user123",
  "user_input": "我想创作一个奇幻小说",
  "additional_data": {...}  # 可选
}

# 获取对话总结
GET /conversation/summary/{user_id}

# 结束对话
DELETE /conversation/end/{user_id}
```

### 支持的数据类型

- `world_setting` - 世界设定
- `cultivation_system` - 修炼体系
- `character` - 人物角色
- `faction` - 势力组织
- `plot` - 剧情内容
- `chapter` - 章节内容
- `volume` - 卷宗内容
- `timeline` - 时间线事件
- `political_system` - 政治体系
- `currency_system` - 货币体系
- `commerce_system` - 商业体系
- `race_system` - 种族体系
- `martial_arts_system` - 功法体系
- `equipment_system` - 装备体系
- `pet_system` - 宠物体系
- `map_structure` - 地图结构
- `dimension_structure` - 维度结构
- `resource_distribution` - 资源分布
- `race_distribution` - 种族分布
- `secret_realm_distribution` - 秘境分布
- `spiritual_treasure_system` - 灵宝体系
- `civilian_system` - 生民体系
- `judicial_system` - 司法体系
- `profession_system` - 职业体系

## 🤖 Agent-AI编剧协作系统

### 📋 功能概述

NovelCraft集成了先进的多AI协作系统，能够协同完成小说从命题到完本的全过程创作。各AI助手分工明确，互相协作，支持人工干预和反馈优化，实现真正的智能化创作流程。

### 🎭 主要AI助手模块

#### 1. 编剧AI（Director AI）
- **功能**: 根据用户给定的方向和命题，生成详细的大纲、世界观、人物设定、剧情主线等
- **输入**: 命题、方向、主题关键词、对话历史
- **输出**: 大纲、设定、主线剧情、章节规划
- **智能特性**: 基于对话历史的个性化生成、多方案对比与推荐、设定完整性检查

#### 2. 剧情写作AI（Writer AI，RWKV为主）
- **功能**: 结合主题、大纲、时间线、设定等，分章节生成详细剧情文本
- **输入**: 大纲、章节规划、设定、时间线、用户偏好
- **输出**: 章节详细内容、场景描述、对话文本
- **智能特性**: 风格一致性保持、伏笔与呼应自动处理、情节紧张度控制

#### 3. 总结AI（Summarizer AI）
- **功能**: 对每章节、每卷（部）进行总结，生成下一卷宗的前言
- **输入**: 章节内容、卷内容、关键情节
- **输出**: 章节总结、卷总结、前言、关键信息提取
- **智能特性**: 多层次总结（简要、详细、关键点）、情节连贯性分析、重要信息标记

#### 4. 读者AI（Reader AI）
- **功能**: 模拟读者对内容进行正向/负向评价，提出建议，辅助编剧AI和写作AI调整剧情和文风
- **输入**: 章节内容、卷内容、总结、目标读者群体
- **输出**: 评价、建议、反馈、改进方案
- **智能特性**: 多角度评价（情节、人物、文笔、逻辑）、读者群体模拟、市场接受度预测

#### 5. 设定管理AI（Setting Manager AI）
- **功能**: 智能管理和维护所有设定信息，确保一致性和完整性
- **输入**: 各类设定信息、新增内容、修改请求
- **输出**: 设定更新、冲突检测、补充建议
- **智能特性**: 设定关联性分析、逻辑冲突自动检测、缺失信息智能补全

### 🔄 AI协作工作流程

```
1. 用户输入命题/方向
   ↓
2. 编剧AI生成大纲、设定、主线
   ↓
3. 剧情写作AI分章节生成内容
   ↓
4. 总结AI对章节/卷进行总结，生成前言
   ↓
5. 读者AI给出评价和建议
   ↓
6. 编剧AI/写作AI根据反馈调整内容
   ↓
7. 循环迭代，直至小说完本
```

### 🧠 智能对话创作流程

#### 阶段1: 初始对话（主题确定）
- 开放式询问收集创作意图
- 意图深化和题材类型确认
- 主题概述生成和用户确认

#### 阶段2: 世界设定构建
- 世界观框架建立
- 地理环境设计
- 历史背景构建
- 自然法则定义

#### 阶段3: 体系设定构建
- 政权体系设计
- 货币体系建立
- 修炼/能力体系构建
- 体系整合和一致性检查

#### 阶段4: 人物角色构建
- 主角设定创建
- 重要配角设计
- 反派角色构建
- 关系网络生成

#### 阶段5: 剧情架构设计
- 整体结构规划
- 卷宗设计
- 章节规划
- 剧情走向确定

### 🔧 技术架构特性

#### AI服务抽象层
- 统一的AI Provider接口
- 插件化的AI模型管理
- 动态加载和配置机制
- 多模型并行处理支持

#### 任务队列与流程引擎
- 任务调度器：管理AI任务的分配和执行
- 状态机：跟踪对话和创作流程状态
- 消息队列：处理AI间的异步通信
- 工作流引擎：定义和执行复杂的创作流程

#### 可视化流程管理
- 流程图展示：显示当前创作进度和AI协作状态
- 实时监控：监控各AI模块的工作状态
- 交互控制：允许用户干预和调整AI协作流程
- 历史追踪：记录和回放创作过程

### 📁 代码结构设计

```
ai_engine/
├── director/           # 编剧AI相关逻辑（deepseek-r1）
│   └── director_ai.py
├── writer/            # 剧情写作AI（RWKV7-G1）
│   └── writer_ai.py
├── summarizer/        # 总结AI（GLM4-LONG）
│   └── summarizer_ai.py
├── reader/            # 读者AI（QWEN3）
│   └── reader_ai.py
├── setting_manager/   # 设定管理AI
│   └── setting_ai.py
├── workflow/          # AI协作与流程管理（Agent）
│   └── workflow_engine.py
├── conversation/      # 智能对话引擎
│   ├── conversation_manager.py
│   ├── intent_analyzer.py
│   └── stage_manager.py
└── utils/            # 工具函数
    ├── ai_provider.py
    └── task_queue.py
```

### 🚀 扩展性说明

- **模块化设计**: 每个AI助手为独立子模块，便于维护和扩展
- **统一接口**: 支持多模型、多平台切换
- **协作机制**: 支持任务队列和流程引擎，便于多AI协作
- **可视化管理**: 前端可视化展示AI协作流程、内容流转和反馈
- **人工干预**: 支持用户在任何阶段介入和调整

### 🔒 安全与隐私

- **本地优先**: 推荐优先支持Ollama、RWKV等本地模型，保障数据安全和隐私
- **云端补充**: 支持智谱AI、DeepSeek、硅基流动等云端模型作为补充
- **数据隔离**: 项目数据完全隔离，确保创作内容的安全性
- **权限控制**: AI助手具备项目级的精确权限控制

### 🎯 使用方式

1. **系统设置配置**: 通过系统设置界面配置各AI助手参数
2. **智能对话创作**: 通过Agent-AI助手进行智能对话，逐步构建项目
3. **协作创作**: 在创作流程中选择AI助手自动/手动生成内容
4. **灵活控制**: 支持一键生成、逐步生成、人工编辑与反馈

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有为NovelCraft项目做出贡献的开发者和用户。

## 📞 联系方式

如有问题或建议，请提交Issue或联系开发团队。