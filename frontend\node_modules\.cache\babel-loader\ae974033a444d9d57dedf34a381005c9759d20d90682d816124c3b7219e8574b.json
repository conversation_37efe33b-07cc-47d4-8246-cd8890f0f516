{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Progress, Tag, Space, Typography, message, Spin } from 'antd';\nimport { ProjectOutlined, UserOutlined, BookOutlined, FileTextOutlined, PlusOutlined, EditOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalProjects: 0,\n    totalCharacters: 0,\n    totalVolumes: 0,\n    totalWords: 0\n  });\n  const [recentProjects, setRecentProjects] = useState([]);\n  const [recentActivities, setRecentActivities] = useState([]);\n\n  // 获取仪表盘数据\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // 获取项目列表\n      const projectsResponse = await axios.get('/api/projects?limit=5');\n      const projects = projectsResponse.data.projects || [];\n\n      // 计算统计数据\n      const totalProjects = projectsResponse.data.total || 0;\n      let totalCharacters = 0;\n      let totalVolumes = 0;\n      let totalWords = 0;\n\n      // 为每个项目获取详细统计\n      for (const project of projects) {\n        totalCharacters += project.character_count || 0;\n        totalVolumes += project.volume_count || 0;\n        totalWords += project.word_count || 0;\n      }\n      setStats({\n        totalProjects,\n        totalCharacters,\n        totalVolumes,\n        totalWords\n      });\n\n      // 设置最近项目（格式化数据）\n      const formattedProjects = projects.map(project => {\n        var _project$progress;\n        return {\n          id: project.id,\n          name: project.name,\n          type: project.project_type,\n          status: project.status,\n          progress: ((_project$progress = project.progress) === null || _project$progress === void 0 ? void 0 : _project$progress.completion_percentage) || 0,\n          lastModified: new Date(project.updated_at).toLocaleDateString(),\n          wordCount: project.word_count || 0\n        };\n      });\n      setRecentProjects(formattedProjects);\n\n      // 生成最近活动（基于项目更新时间）\n      const activities = projects.slice(0, 4).map((project, index) => ({\n        id: index + 1,\n        type: 'project',\n        action: '更新了项目',\n        target: project.name,\n        project: '',\n        time: getRelativeTime(project.updated_at)\n      }));\n      setRecentActivities(activities);\n    } catch (error) {\n      console.error('获取仪表盘数据失败:', error);\n      message.error('获取仪表盘数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 计算相对时间\n  const getRelativeTime = dateString => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffMs = now - date;\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n    if (diffDays > 0) {\n      return `${diffDays}天前`;\n    } else if (diffHours > 0) {\n      return `${diffHours}小时前`;\n    } else {\n      return '刚刚';\n    }\n  };\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  const getActivityIcon = type => {\n    const icons = {\n      project: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 16\n      }, this),\n      character: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 18\n      }, this),\n      chapter: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 16\n      }, this),\n      plot: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 13\n      }, this)\n    };\n    return icons[type] || /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 27\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u4EEA\\u8868\\u76D8\\u6570\\u636E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u4EEA\\u8868\\u76D8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        className: \"page-description\",\n        children: \"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF01\\u67E5\\u770B\\u60A8\\u7684\\u521B\\u4F5C\\u6982\\u51B5\\u548C\\u6700\\u8FD1\\u6D3B\\u52A8\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9879\\u76EE\\u603B\\u6570\",\n            value: stats.totalProjects,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EBA\\u7269\\u603B\\u6570\",\n            value: stats.totalCharacters,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5377\\u5B97\\u603B\\u6570\",\n            value: stats.totalVolumes,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: stats.totalWords,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            },\n            formatter: value => `${(value / 10000).toFixed(1)}万`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 14,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u9879\\u76EE\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 23\n            }, this),\n            onClick: () => navigate('/projects'),\n            children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: recentProjects,\n            renderItem: project => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                onClick: () => navigate(`/projects/${project.id}`),\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: project.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: getStatusColor(project.status),\n                    children: getStatusText(project.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    children: getTypeText(project.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  style: {\n                    width: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5B57\\u6570: \", (project.wordCount / 10000).toFixed(1), \"\\u4E07 | \\u6700\\u540E\\u4FEE\\u6539: \", project.lastModified]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: project.progress,\n                    size: \"small\",\n                    status: project.progress === 100 ? 'success' : 'active'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 10,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u8FD1\\u6D3B\\u52A8\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: recentActivities,\n            renderItem: activity => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getActivityIcon(activity.type),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: activity.action\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: activity.target\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [activity.project && /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u9879\\u76EE: \", activity.project]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 27\n                    }, this), \" \", activity.time]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/projects'),\n            children: \"\\u521B\\u5EFA\\u65B0\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/characters'),\n            children: \"\\u6DFB\\u52A0\\u4EBA\\u7269\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/volumes'),\n            children: \"\\u7BA1\\u7406\\u5377\\u5B97\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            block: true,\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate('/ai-assistant'),\n            children: \"AI\\u52A9\\u624B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"Uf23Cu/vKqJxLn/UR/dGBiFUzow=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Progress", "Tag", "Space", "Typography", "message", "Spin", "ProjectOutlined", "UserOutlined", "BookOutlined", "FileTextOutlined", "PlusOutlined", "EditOutlined", "ClockCircleOutlined", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "Dashboard", "_s", "navigate", "loading", "setLoading", "stats", "setStats", "totalProjects", "totalCharacters", "totalVolumes", "totalWords", "recentProjects", "setRecentProjects", "recentActivities", "setRecentActivities", "fetchDashboardData", "projectsResponse", "get", "projects", "data", "total", "project", "character_count", "volume_count", "word_count", "formattedProjects", "map", "_project$progress", "id", "name", "type", "project_type", "status", "progress", "completion_percentage", "lastModified", "Date", "updated_at", "toLocaleDateString", "wordCount", "activities", "slice", "index", "action", "target", "time", "getRelativeTime", "error", "console", "dateString", "now", "date", "diffMs", "diffHours", "Math", "floor", "diffDays", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "getActivityIcon", "icons", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "character", "chapter", "plot", "className", "style", "textAlign", "padding", "children", "size", "marginTop", "level", "gutter", "xs", "sm", "lg", "title", "value", "prefix", "valueStyle", "color", "formatter", "toFixed", "extra", "icon", "onClick", "dataSource", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "description", "direction", "width", "percent", "activity", "avatar", "strong", "md", "block", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Progress, Tag, Space, Typography, message, Spin } from 'antd';\nimport {\n  ProjectOutlined,\n  UserOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalProjects: 0,\n    totalCharacters: 0,\n    totalVolumes: 0,\n    totalWords: 0\n  });\n\n  const [recentProjects, setRecentProjects] = useState([]);\n  const [recentActivities, setRecentActivities] = useState([]);\n\n  // 获取仪表盘数据\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // 获取项目列表\n      const projectsResponse = await axios.get('/api/projects?limit=5');\n      const projects = projectsResponse.data.projects || [];\n\n      // 计算统计数据\n      const totalProjects = projectsResponse.data.total || 0;\n      let totalCharacters = 0;\n      let totalVolumes = 0;\n      let totalWords = 0;\n\n      // 为每个项目获取详细统计\n      for (const project of projects) {\n        totalCharacters += project.character_count || 0;\n        totalVolumes += project.volume_count || 0;\n        totalWords += project.word_count || 0;\n      }\n\n      setStats({\n        totalProjects,\n        totalCharacters,\n        totalVolumes,\n        totalWords\n      });\n\n      // 设置最近项目（格式化数据）\n      const formattedProjects = projects.map(project => ({\n        id: project.id,\n        name: project.name,\n        type: project.project_type,\n        status: project.status,\n        progress: project.progress?.completion_percentage || 0,\n        lastModified: new Date(project.updated_at).toLocaleDateString(),\n        wordCount: project.word_count || 0\n      }));\n\n      setRecentProjects(formattedProjects);\n\n      // 生成最近活动（基于项目更新时间）\n      const activities = projects.slice(0, 4).map((project, index) => ({\n        id: index + 1,\n        type: 'project',\n        action: '更新了项目',\n        target: project.name,\n        project: '',\n        time: getRelativeTime(project.updated_at)\n      }));\n\n      setRecentActivities(activities);\n\n    } catch (error) {\n      console.error('获取仪表盘数据失败:', error);\n      message.error('获取仪表盘数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 计算相对时间\n  const getRelativeTime = (dateString) => {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffMs = now - date;\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffHours / 24);\n\n    if (diffDays > 0) {\n      return `${diffDays}天前`;\n    } else if (diffHours > 0) {\n      return `${diffHours}小时前`;\n    } else {\n      return '刚刚';\n    }\n  };\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  const getActivityIcon = (type) => {\n    const icons = {\n      project: <ProjectOutlined />,\n      character: <UserOutlined />,\n      chapter: <FileTextOutlined />,\n      plot: <BookOutlined />\n    };\n    return icons[type] || <EditOutlined />;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"fade-in\" style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>\n          <Text>正在加载仪表盘数据...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">仪表盘</Title>\n        <Text className=\"page-description\">欢迎回来！查看您的创作概况和最近活动。</Text>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} className=\"stats-grid\">\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"项目总数\"\n              value={stats.totalProjects}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"人物总数\"\n              value={stats.totalCharacters}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"卷宗总数\"\n              value={stats.totalVolumes}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={stats.totalWords}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n              formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        {/* 最近项目 */}\n        <Col xs={24} lg={14}>\n          <Card\n            title=\"最近项目\"\n            extra={\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => navigate('/projects')}\n              >\n                新建项目\n              </Button>\n            }\n          >\n            <List\n              dataSource={recentProjects}\n              renderItem={(project) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      onClick={() => navigate(`/projects/${project.id}`)}\n                    >\n                      查看详情\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        <span>{project.name}</span>\n                        <Tag color={getStatusColor(project.status)}>\n                          {getStatusText(project.status)}\n                        </Tag>\n                        <Tag>{getTypeText(project.type)}</Tag>\n                      </Space>\n                    }\n                    description={\n                      <Space direction=\"vertical\" style={{ width: '100%' }}>\n                        <div>\n                          <Text type=\"secondary\">\n                            字数: {(project.wordCount / 10000).toFixed(1)}万 |\n                            最后修改: {project.lastModified}\n                          </Text>\n                        </div>\n                        <Progress\n                          percent={project.progress}\n                          size=\"small\"\n                          status={project.progress === 100 ? 'success' : 'active'}\n                        />\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 最近活动 */}\n        <Col xs={24} lg={10}>\n          <Card title=\"最近活动\">\n            <List\n              dataSource={recentActivities}\n              renderItem={(activity) => (\n                <List.Item>\n                  <List.Item.Meta\n                    avatar={getActivityIcon(activity.type)}\n                    title={\n                      <Space>\n                        <span>{activity.action}</span>\n                        <Text strong>{activity.target}</Text>\n                      </Space>\n                    }\n                    description={\n                      <Space>\n                        {activity.project && (\n                          <Text type=\"secondary\">项目: {activity.project}</Text>\n                        )}\n                        <Text type=\"secondary\">\n                          <ClockCircleOutlined /> {activity.time}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快速操作 */}\n      <Card title=\"快速操作\" style={{ marginTop: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<ProjectOutlined />}\n              onClick={() => navigate('/projects')}\n            >\n              创建新项目\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<UserOutlined />}\n              onClick={() => navigate('/characters')}\n            >\n              添加人物\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<FileTextOutlined />}\n              onClick={() => navigate('/volumes')}\n            >\n              管理卷宗\n            </Button>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Button\n              type=\"dashed\"\n              block\n              size=\"large\"\n              icon={<BookOutlined />}\n              onClick={() => navigate('/ai-assistant')}\n            >\n              AI助手\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AAC/G,SACEC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,mBAAmB,QACd,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGf,UAAU;AAElC,MAAMgB,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC;IACjCkC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM0C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMY,gBAAgB,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,uBAAuB,CAAC;MACjE,MAAMC,QAAQ,GAAGF,gBAAgB,CAACG,IAAI,CAACD,QAAQ,IAAI,EAAE;;MAErD;MACA,MAAMX,aAAa,GAAGS,gBAAgB,CAACG,IAAI,CAACC,KAAK,IAAI,CAAC;MACtD,IAAIZ,eAAe,GAAG,CAAC;MACvB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIC,UAAU,GAAG,CAAC;;MAElB;MACA,KAAK,MAAMW,OAAO,IAAIH,QAAQ,EAAE;QAC9BV,eAAe,IAAIa,OAAO,CAACC,eAAe,IAAI,CAAC;QAC/Cb,YAAY,IAAIY,OAAO,CAACE,YAAY,IAAI,CAAC;QACzCb,UAAU,IAAIW,OAAO,CAACG,UAAU,IAAI,CAAC;MACvC;MAEAlB,QAAQ,CAAC;QACPC,aAAa;QACbC,eAAe;QACfC,YAAY;QACZC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMe,iBAAiB,GAAGP,QAAQ,CAACQ,GAAG,CAACL,OAAO;QAAA,IAAAM,iBAAA;QAAA,OAAK;UACjDC,EAAE,EAAEP,OAAO,CAACO,EAAE;UACdC,IAAI,EAAER,OAAO,CAACQ,IAAI;UAClBC,IAAI,EAAET,OAAO,CAACU,YAAY;UAC1BC,MAAM,EAAEX,OAAO,CAACW,MAAM;UACtBC,QAAQ,EAAE,EAAAN,iBAAA,GAAAN,OAAO,CAACY,QAAQ,cAAAN,iBAAA,uBAAhBA,iBAAA,CAAkBO,qBAAqB,KAAI,CAAC;UACtDC,YAAY,EAAE,IAAIC,IAAI,CAACf,OAAO,CAACgB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAC/DC,SAAS,EAAElB,OAAO,CAACG,UAAU,IAAI;QACnC,CAAC;MAAA,CAAC,CAAC;MAEHZ,iBAAiB,CAACa,iBAAiB,CAAC;;MAEpC;MACA,MAAMe,UAAU,GAAGtB,QAAQ,CAACuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACf,GAAG,CAAC,CAACL,OAAO,EAAEqB,KAAK,MAAM;QAC/Dd,EAAE,EAAEc,KAAK,GAAG,CAAC;QACbZ,IAAI,EAAE,SAAS;QACfa,MAAM,EAAE,OAAO;QACfC,MAAM,EAAEvB,OAAO,CAACQ,IAAI;QACpBR,OAAO,EAAE,EAAE;QACXwB,IAAI,EAAEC,eAAe,CAACzB,OAAO,CAACgB,UAAU;MAC1C,CAAC,CAAC,CAAC;MAEHvB,mBAAmB,CAAC0B,UAAU,CAAC;IAEjC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC9D,OAAO,CAAC8D,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0C,eAAe,GAAIG,UAAU,IAAK;IACtC,MAAMC,GAAG,GAAG,IAAId,IAAI,CAAC,CAAC;IACtB,MAAMe,IAAI,GAAG,IAAIf,IAAI,CAACa,UAAU,CAAC;IACjC,MAAMG,MAAM,GAAGF,GAAG,GAAGC,IAAI;IACzB,MAAME,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMI,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC;IAE3C,IAAIG,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,GAAGA,QAAQ,IAAI;IACxB,CAAC,MAAM,IAAIH,SAAS,GAAG,CAAC,EAAE;MACxB,OAAO,GAAGA,SAAS,KAAK;IAC1B,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC;EAED/E,SAAS,CAAC,MAAM;IACdyC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0C,cAAc,GAAIzB,MAAM,IAAK;IACjC,MAAM0B,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAAC1B,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMgC,aAAa,GAAIhC,MAAM,IAAK;IAChC,MAAMiC,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACjC,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMkC,WAAW,GAAIpC,IAAI,IAAK;IAC5B,MAAMmC,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAACnC,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAM4C,eAAe,GAAI5C,IAAI,IAAK;IAChC,MAAM6C,KAAK,GAAG;MACZtD,OAAO,eAAExB,OAAA,CAACV,eAAe;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5BC,SAAS,eAAEnF,OAAA,CAACT,YAAY;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3BE,OAAO,eAAEpF,OAAA,CAACP,gBAAgB;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC7BG,IAAI,eAAErF,OAAA,CAACR,YAAY;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACvB,CAAC;IACD,OAAOJ,KAAK,CAAC7C,IAAI,CAAC,iBAAIjC,OAAA,CAACL,YAAY;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC,CAAC;EAED,IAAI5E,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKsF,SAAS,EAAC,SAAS;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACvE1F,OAAA,CAACX,IAAI;QAACsG,IAAI,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBlF,OAAA;QAAKuF,KAAK,EAAE;UAAEK,SAAS,EAAE;QAAG,CAAE;QAAAF,QAAA,eAC5B1F,OAAA,CAACE,IAAI;UAAAwF,QAAA,EAAC;QAAY;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAKsF,SAAS,EAAC,SAAS;IAAAI,QAAA,gBACtB1F,OAAA;MAAKsF,SAAS,EAAC,aAAa;MAAAI,QAAA,gBAC1B1F,OAAA,CAACC,KAAK;QAAC4F,KAAK,EAAE,CAAE;QAACP,SAAS,EAAC,YAAY;QAAAI,QAAA,EAAC;MAAG;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDlF,OAAA,CAACE,IAAI;QAACoF,SAAS,EAAC,kBAAkB;QAAAI,QAAA,EAAC;MAAmB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGNlF,OAAA,CAACrB,GAAG;MAACmH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACR,SAAS,EAAC,YAAY;MAAAI,QAAA,gBAC3C1F,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eACzB1F,OAAA,CAACtB,IAAI;UAAAgH,QAAA,eACH1F,OAAA,CAACnB,SAAS;YACRqH,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE3F,KAAK,CAACE,aAAc;YAC3B0F,MAAM,eAAEpG,OAAA,CAACV,eAAe;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eACzB1F,OAAA,CAACtB,IAAI;UAAAgH,QAAA,eACH1F,OAAA,CAACnB,SAAS;YACRqH,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE3F,KAAK,CAACG,eAAgB;YAC7ByF,MAAM,eAAEpG,OAAA,CAACT,YAAY;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eACzB1F,OAAA,CAACtB,IAAI;UAAAgH,QAAA,eACH1F,OAAA,CAACnB,SAAS;YACRqH,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE3F,KAAK,CAACI,YAAa;YAC1BwF,MAAM,eAAEpG,OAAA,CAACP,gBAAgB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eACzB1F,OAAA,CAACtB,IAAI;UAAAgH,QAAA,eACH1F,OAAA,CAACnB,SAAS;YACRqH,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE3F,KAAK,CAACK,UAAW;YACxBuF,MAAM,eAAEpG,OAAA,CAACR,YAAY;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjCC,SAAS,EAAGJ,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEK,OAAO,CAAC,CAAC,CAAC;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlF,OAAA,CAACrB,GAAG;MAACmH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACP,KAAK,EAAE;QAAEK,SAAS,EAAE;MAAG,CAAE;MAAAF,QAAA,gBAE9C1F,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAP,QAAA,eAClB1F,OAAA,CAACtB,IAAI;UACHwH,KAAK,EAAC,0BAAM;UACZO,KAAK,eACHzG,OAAA,CAACjB,MAAM;YACLkD,IAAI,EAAC,SAAS;YACdyE,IAAI,eAAE1G,OAAA,CAACN,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,WAAW,CAAE;YAAAqF,QAAA,EACtC;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAQ,QAAA,eAED1F,OAAA,CAAClB,IAAI;YACH8H,UAAU,EAAE9F,cAAe;YAC3B+F,UAAU,EAAGrF,OAAO,iBAClBxB,OAAA,CAAClB,IAAI,CAACgI,IAAI;cACRC,OAAO,EAAE,cACP/G,OAAA,CAACjB,MAAM;gBACLkD,IAAI,EAAC,MAAM;gBACX0E,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,aAAamB,OAAO,CAACO,EAAE,EAAE,CAAE;gBAAA2D,QAAA,EACpD;cAED;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAQ,QAAA,eAEF1F,OAAA,CAAClB,IAAI,CAACgI,IAAI,CAACE,IAAI;gBACbd,KAAK,eACHlG,OAAA,CAACd,KAAK;kBAAAwG,QAAA,gBACJ1F,OAAA;oBAAA0F,QAAA,EAAOlE,OAAO,CAACQ;kBAAI;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BlF,OAAA,CAACf,GAAG;oBAACqH,KAAK,EAAE1C,cAAc,CAACpC,OAAO,CAACW,MAAM,CAAE;oBAAAuD,QAAA,EACxCvB,aAAa,CAAC3C,OAAO,CAACW,MAAM;kBAAC;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNlF,OAAA,CAACf,GAAG;oBAAAyG,QAAA,EAAErB,WAAW,CAAC7C,OAAO,CAACS,IAAI;kBAAC;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CACR;gBACD+B,WAAW,eACTjH,OAAA,CAACd,KAAK;kBAACgI,SAAS,EAAC,UAAU;kBAAC3B,KAAK,EAAE;oBAAE4B,KAAK,EAAE;kBAAO,CAAE;kBAAAzB,QAAA,gBACnD1F,OAAA;oBAAA0F,QAAA,eACE1F,OAAA,CAACE,IAAI;sBAAC+B,IAAI,EAAC,WAAW;sBAAAyD,QAAA,GAAC,gBACjB,EAAC,CAAClE,OAAO,CAACkB,SAAS,GAAG,KAAK,EAAE8D,OAAO,CAAC,CAAC,CAAC,EAAC,qCACtC,EAAChF,OAAO,CAACc,YAAY;oBAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlF,OAAA,CAAChB,QAAQ;oBACPoI,OAAO,EAAE5F,OAAO,CAACY,QAAS;oBAC1BuD,IAAI,EAAC,OAAO;oBACZxD,MAAM,EAAEX,OAAO,CAACY,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;kBAAS;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlF,OAAA,CAACpB,GAAG;QAACmH,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAP,QAAA,eAClB1F,OAAA,CAACtB,IAAI;UAACwH,KAAK,EAAC,0BAAM;UAAAR,QAAA,eAChB1F,OAAA,CAAClB,IAAI;YACH8H,UAAU,EAAE5F,gBAAiB;YAC7B6F,UAAU,EAAGQ,QAAQ,iBACnBrH,OAAA,CAAClB,IAAI,CAACgI,IAAI;cAAApB,QAAA,eACR1F,OAAA,CAAClB,IAAI,CAACgI,IAAI,CAACE,IAAI;gBACbM,MAAM,EAAEzC,eAAe,CAACwC,QAAQ,CAACpF,IAAI,CAAE;gBACvCiE,KAAK,eACHlG,OAAA,CAACd,KAAK;kBAAAwG,QAAA,gBACJ1F,OAAA;oBAAA0F,QAAA,EAAO2B,QAAQ,CAACvE;kBAAM;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BlF,OAAA,CAACE,IAAI;oBAACqH,MAAM;oBAAA7B,QAAA,EAAE2B,QAAQ,CAACtE;kBAAM;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACR;gBACD+B,WAAW,eACTjH,OAAA,CAACd,KAAK;kBAAAwG,QAAA,GACH2B,QAAQ,CAAC7F,OAAO,iBACfxB,OAAA,CAACE,IAAI;oBAAC+B,IAAI,EAAC,WAAW;oBAAAyD,QAAA,GAAC,gBAAI,EAAC2B,QAAQ,CAAC7F,OAAO;kBAAA;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACpD,eACDlF,OAAA,CAACE,IAAI;oBAAC+B,IAAI,EAAC,WAAW;oBAAAyD,QAAA,gBACpB1F,OAAA,CAACJ,mBAAmB;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAACmC,QAAQ,CAACrE,IAAI;kBAAA;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlF,OAAA,CAACtB,IAAI;MAACwH,KAAK,EAAC,0BAAM;MAACX,KAAK,EAAE;QAAEK,SAAS,EAAE;MAAG,CAAE;MAAAF,QAAA,eAC1C1F,OAAA,CAACrB,GAAG;QAACmH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAJ,QAAA,gBACpB1F,OAAA,CAACpB,GAAG;UAACmH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACwB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACzB1F,OAAA,CAACjB,MAAM;YACLkD,IAAI,EAAC,QAAQ;YACbwF,KAAK;YACL9B,IAAI,EAAC,OAAO;YACZe,IAAI,eAAE1G,OAAA,CAACV,eAAe;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1ByB,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,WAAW,CAAE;YAAAqF,QAAA,EACtC;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlF,OAAA,CAACpB,GAAG;UAACmH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACwB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACzB1F,OAAA,CAACjB,MAAM;YACLkD,IAAI,EAAC,QAAQ;YACbwF,KAAK;YACL9B,IAAI,EAAC,OAAO;YACZe,IAAI,eAAE1G,OAAA,CAACT,YAAY;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,aAAa,CAAE;YAAAqF,QAAA,EACxC;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlF,OAAA,CAACpB,GAAG;UAACmH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACwB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACzB1F,OAAA,CAACjB,MAAM;YACLkD,IAAI,EAAC,QAAQ;YACbwF,KAAK;YACL9B,IAAI,EAAC,OAAO;YACZe,IAAI,eAAE1G,OAAA,CAACP,gBAAgB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3ByB,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,UAAU,CAAE;YAAAqF,QAAA,EACrC;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlF,OAAA,CAACpB,GAAG;UAACmH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACwB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACzB1F,OAAA,CAACjB,MAAM;YACLkD,IAAI,EAAC,QAAQ;YACbwF,KAAK;YACL9B,IAAI,EAAC,OAAO;YACZe,IAAI,eAAE1G,OAAA,CAACR,YAAY;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAMtG,QAAQ,CAAC,eAAe,CAAE;YAAAqF,QAAA,EAC1C;UAED;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA5VID,SAAS;EAAA,QACIN,WAAW;AAAA;AAAA6H,EAAA,GADxBvH,SAAS;AA8Vf,eAAeA,SAAS;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}