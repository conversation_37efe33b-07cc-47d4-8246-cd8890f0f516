{"ast": null, "code": "/**\n * API 配置和工具函数\n */\nimport axios from 'axios';\nimport { message } from 'antd';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  var _config$method;\n  // 在发送请求之前做些什么\n  console.log('API Request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url);\n  return config;\n}, error => {\n  // 对请求错误做些什么\n  console.error('Request Error:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  // 对响应数据做点什么\n  console.log('API Response:', response.status, response.config.url);\n  return response;\n}, error => {\n  // 对响应错误做点什么\n  console.error('Response Error:', error);\n  if (error.response) {\n    // 服务器响应了错误状态码\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 400:\n        message.error(data.detail || '请求参数错误');\n        break;\n      case 401:\n        message.error('未授权访问');\n        break;\n      case 403:\n        message.error('禁止访问');\n        break;\n      case 404:\n        message.error('资源不存在');\n        break;\n      case 500:\n        message.error('服务器内部错误');\n        break;\n      default:\n        message.error(data.detail || '请求失败');\n    }\n  } else if (error.request) {\n    // 请求已发出但没有收到响应\n    message.error('网络连接失败，请检查网络设置');\n  } else {\n    // 其他错误\n    message.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\n\n// API方法\nexport const projectAPI = {\n  // 获取项目列表\n  getProjects: (params = {}) => api.get('/projects', {\n    params\n  }),\n  // 获取项目详情\n  getProject: id => api.get(`/projects/${id}`),\n  // 创建项目\n  createProject: data => api.post('/projects', data),\n  // 更新项目\n  updateProject: (id, data) => api.put(`/projects/${id}`, data),\n  // 删除项目\n  deleteProject: id => api.delete(`/projects/${id}`),\n  // 复制项目\n  duplicateProject: (id, newName) => api.post(`/projects/${id}/duplicate`, null, {\n    params: {\n      new_name: newName\n    }\n  }),\n  // 导出项目\n  exportProject: (id, format = 'json') => api.post(`/projects/${id}/export`, {\n    format\n  }),\n  // 获取项目统计\n  getProjectStatistics: id => api.get(`/projects/${id}/statistics`)\n};\nexport const projectDataAPI = {\n  // 获取项目数据\n  getProjectData: (projectId, modelName = null) => {\n    const params = modelName ? {\n      model_name: modelName\n    } : {};\n    return api.get(`/project-data/projects/${projectId}/data`, {\n      params\n    });\n  },\n  // 创建项目数据\n  createProjectData: (projectId, modelName, data) => api.post(`/project-data/projects/${projectId}/data/${modelName}`, data),\n  // 更新项目数据\n  updateProjectData: (projectId, modelName, itemId, data) => api.put(`/project-data/projects/${projectId}/data/${modelName}/${itemId}`, data),\n  // 删除项目数据\n  deleteProjectData: (projectId, modelName, itemId) => api.delete(`/project-data/projects/${projectId}/data/${modelName}/${itemId}`)\n};\nexport const aiAPI = {\n  // 获取AI提供商列表\n  getProviders: () => api.get('/ai/providers'),\n  // 获取AI状态\n  getStatus: () => api.get('/ai/status'),\n  // 获取AI配置\n  getConfig: provider => api.get(`/ai/config/${provider}`),\n  // 保存AI配置\n  saveConfig: data => api.post('/ai/config', data),\n  // AI对话\n  chat: data => api.post('/ai/chat', data),\n  // 获取Ollama模型列表\n  getOllamaModels: () => api.get('/ai/ollama/models')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "message", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "detail", "projectAPI", "getProjects", "params", "get", "getProject", "id", "createProject", "post", "updateProject", "put", "deleteProject", "delete", "duplicateProject", "newName", "new_name", "exportProject", "format", "getProjectStatistics", "projectDataAPI", "getProjectData", "projectId", "modelName", "model_name", "createProjectData", "updateProjectData", "itemId", "deleteProjectData", "aiAPI", "getProviders", "getStatus", "getConfig", "provider", "saveConfig", "chat", "getOllamaModels"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/utils/api.js"], "sourcesContent": ["/**\n * API 配置和工具函数\n */\nimport axios from 'axios';\nimport { message } from 'antd';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 在发送请求之前做些什么\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n  },\n  (error) => {\n    // 对请求错误做些什么\n    console.error('Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    // 对响应数据做点什么\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n  },\n  (error) => {\n    // 对响应错误做点什么\n    console.error('Response Error:', error);\n    \n    if (error.response) {\n      // 服务器响应了错误状态码\n      const { status, data } = error.response;\n      \n      switch (status) {\n        case 400:\n          message.error(data.detail || '请求参数错误');\n          break;\n        case 401:\n          message.error('未授权访问');\n          break;\n        case 403:\n          message.error('禁止访问');\n          break;\n        case 404:\n          message.error('资源不存在');\n          break;\n        case 500:\n          message.error('服务器内部错误');\n          break;\n        default:\n          message.error(data.detail || '请求失败');\n      }\n    } else if (error.request) {\n      // 请求已发出但没有收到响应\n      message.error('网络连接失败，请检查网络设置');\n    } else {\n      // 其他错误\n      message.error('请求配置错误');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// API方法\nexport const projectAPI = {\n  // 获取项目列表\n  getProjects: (params = {}) => api.get('/projects', { params }),\n  \n  // 获取项目详情\n  getProject: (id) => api.get(`/projects/${id}`),\n  \n  // 创建项目\n  createProject: (data) => api.post('/projects', data),\n  \n  // 更新项目\n  updateProject: (id, data) => api.put(`/projects/${id}`, data),\n  \n  // 删除项目\n  deleteProject: (id) => api.delete(`/projects/${id}`),\n  \n  // 复制项目\n  duplicateProject: (id, newName) => api.post(`/projects/${id}/duplicate`, null, {\n    params: { new_name: newName }\n  }),\n  \n  // 导出项目\n  exportProject: (id, format = 'json') => api.post(`/projects/${id}/export`, { format }),\n  \n  // 获取项目统计\n  getProjectStatistics: (id) => api.get(`/projects/${id}/statistics`),\n};\n\nexport const projectDataAPI = {\n  // 获取项目数据\n  getProjectData: (projectId, modelName = null) => {\n    const params = modelName ? { model_name: modelName } : {};\n    return api.get(`/project-data/projects/${projectId}/data`, { params });\n  },\n  \n  // 创建项目数据\n  createProjectData: (projectId, modelName, data) => \n    api.post(`/project-data/projects/${projectId}/data/${modelName}`, data),\n  \n  // 更新项目数据\n  updateProjectData: (projectId, modelName, itemId, data) => \n    api.put(`/project-data/projects/${projectId}/data/${modelName}/${itemId}`, data),\n  \n  // 删除项目数据\n  deleteProjectData: (projectId, modelName, itemId) => \n    api.delete(`/project-data/projects/${projectId}/data/${modelName}/${itemId}`),\n};\n\nexport const aiAPI = {\n  // 获取AI提供商列表\n  getProviders: () => api.get('/ai/providers'),\n  \n  // 获取AI状态\n  getStatus: () => api.get('/ai/status'),\n  \n  // 获取AI配置\n  getConfig: (provider) => api.get(`/ai/config/${provider}`),\n  \n  // 保存AI配置\n  saveConfig: (data) => api.post('/ai/config', data),\n  \n  // AI对话\n  chat: (data) => api.post('/ai/chat', data),\n  \n  // 获取Ollama模型列表\n  getOllamaModels: () => api.get('/ai/ollama/models'),\n};\n\nexport default api;\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,MAAM,GAAG,2BAA2B;EACrFC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACV;EACAC,OAAO,CAACC,GAAG,CAAC,cAAc,GAAAF,cAAA,GAAED,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,CAAC;EACrE,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACT;EACAL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlB,GAAG,CAACQ,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZ;EACAR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACV,MAAM,CAACM,GAAG,CAAC;EAClE,OAAOI,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACAL,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;EAEvC,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB;IACA,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGL,KAAK,CAACG,QAAQ;IAEvC,QAAQC,MAAM;MACZ,KAAK,GAAG;QACNvB,OAAO,CAACmB,KAAK,CAACK,IAAI,CAACC,MAAM,IAAI,QAAQ,CAAC;QACtC;MACF,KAAK,GAAG;QACNzB,OAAO,CAACmB,KAAK,CAAC,OAAO,CAAC;QACtB;MACF,KAAK,GAAG;QACNnB,OAAO,CAACmB,KAAK,CAAC,MAAM,CAAC;QACrB;MACF,KAAK,GAAG;QACNnB,OAAO,CAACmB,KAAK,CAAC,OAAO,CAAC;QACtB;MACF,KAAK,GAAG;QACNnB,OAAO,CAACmB,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACEnB,OAAO,CAACmB,KAAK,CAACK,IAAI,CAACC,MAAM,IAAI,MAAM,CAAC;IACxC;EACF,CAAC,MAAM,IAAIN,KAAK,CAACT,OAAO,EAAE;IACxB;IACAV,OAAO,CAACmB,KAAK,CAAC,gBAAgB,CAAC;EACjC,CAAC,MAAM;IACL;IACAnB,OAAO,CAACmB,KAAK,CAAC,QAAQ,CAAC;EACzB;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMO,UAAU,GAAG;EACxB;EACAC,WAAW,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK3B,GAAG,CAAC4B,GAAG,CAAC,WAAW,EAAE;IAAED;EAAO,CAAC,CAAC;EAE9D;EACAE,UAAU,EAAGC,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAE9C;EACAC,aAAa,EAAGR,IAAI,IAAKvB,GAAG,CAACgC,IAAI,CAAC,WAAW,EAAET,IAAI,CAAC;EAEpD;EACAU,aAAa,EAAEA,CAACH,EAAE,EAAEP,IAAI,KAAKvB,GAAG,CAACkC,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEP,IAAI,CAAC;EAE7D;EACAY,aAAa,EAAGL,EAAE,IAAK9B,GAAG,CAACoC,MAAM,CAAC,aAAaN,EAAE,EAAE,CAAC;EAEpD;EACAO,gBAAgB,EAAEA,CAACP,EAAE,EAAEQ,OAAO,KAAKtC,GAAG,CAACgC,IAAI,CAAC,aAAaF,EAAE,YAAY,EAAE,IAAI,EAAE;IAC7EH,MAAM,EAAE;MAAEY,QAAQ,EAAED;IAAQ;EAC9B,CAAC,CAAC;EAEF;EACAE,aAAa,EAAEA,CAACV,EAAE,EAAEW,MAAM,GAAG,MAAM,KAAKzC,GAAG,CAACgC,IAAI,CAAC,aAAaF,EAAE,SAAS,EAAE;IAAEW;EAAO,CAAC,CAAC;EAEtF;EACAC,oBAAoB,EAAGZ,EAAE,IAAK9B,GAAG,CAAC4B,GAAG,CAAC,aAAaE,EAAE,aAAa;AACpE,CAAC;AAED,OAAO,MAAMa,cAAc,GAAG;EAC5B;EACAC,cAAc,EAAEA,CAACC,SAAS,EAAEC,SAAS,GAAG,IAAI,KAAK;IAC/C,MAAMnB,MAAM,GAAGmB,SAAS,GAAG;MAAEC,UAAU,EAAED;IAAU,CAAC,GAAG,CAAC,CAAC;IACzD,OAAO9C,GAAG,CAAC4B,GAAG,CAAC,0BAA0BiB,SAAS,OAAO,EAAE;MAAElB;IAAO,CAAC,CAAC;EACxE,CAAC;EAED;EACAqB,iBAAiB,EAAEA,CAACH,SAAS,EAAEC,SAAS,EAAEvB,IAAI,KAC5CvB,GAAG,CAACgC,IAAI,CAAC,0BAA0Ba,SAAS,SAASC,SAAS,EAAE,EAAEvB,IAAI,CAAC;EAEzE;EACA0B,iBAAiB,EAAEA,CAACJ,SAAS,EAAEC,SAAS,EAAEI,MAAM,EAAE3B,IAAI,KACpDvB,GAAG,CAACkC,GAAG,CAAC,0BAA0BW,SAAS,SAASC,SAAS,IAAII,MAAM,EAAE,EAAE3B,IAAI,CAAC;EAElF;EACA4B,iBAAiB,EAAEA,CAACN,SAAS,EAAEC,SAAS,EAAEI,MAAM,KAC9ClD,GAAG,CAACoC,MAAM,CAAC,0BAA0BS,SAAS,SAASC,SAAS,IAAII,MAAM,EAAE;AAChF,CAAC;AAED,OAAO,MAAME,KAAK,GAAG;EACnB;EACAC,YAAY,EAAEA,CAAA,KAAMrD,GAAG,CAAC4B,GAAG,CAAC,eAAe,CAAC;EAE5C;EACA0B,SAAS,EAAEA,CAAA,KAAMtD,GAAG,CAAC4B,GAAG,CAAC,YAAY,CAAC;EAEtC;EACA2B,SAAS,EAAGC,QAAQ,IAAKxD,GAAG,CAAC4B,GAAG,CAAC,cAAc4B,QAAQ,EAAE,CAAC;EAE1D;EACAC,UAAU,EAAGlC,IAAI,IAAKvB,GAAG,CAACgC,IAAI,CAAC,YAAY,EAAET,IAAI,CAAC;EAElD;EACAmC,IAAI,EAAGnC,IAAI,IAAKvB,GAAG,CAACgC,IAAI,CAAC,UAAU,EAAET,IAAI,CAAC;EAE1C;EACAoC,eAAe,EAAEA,CAAA,KAAM3D,GAAG,CAAC4B,GAAG,CAAC,mBAAmB;AACpD,CAAC;AAED,eAAe5B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}