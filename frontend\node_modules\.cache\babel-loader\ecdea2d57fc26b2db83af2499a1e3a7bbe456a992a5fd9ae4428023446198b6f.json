{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ProjectList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Tag, Progress, Modal, Form, Input, Select, message, Popconfirm, Typography, Row, Col } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, ExportOutlined, ImportOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst ProjectList = () => {\n  _s();\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockProjects = [{\n    id: 1,\n    name: '仙侠传说',\n    title: '九天仙缘录',\n    author: '作者A',\n    type: 'xianxia',\n    status: 'writing',\n    wordCount: 89000,\n    chapterCount: 45,\n    characterCount: 12,\n    progress: 65,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15',\n    isPreset: false\n  }, {\n    id: 2,\n    name: '星际征途',\n    title: '银河帝国崛起',\n    author: '作者B',\n    type: 'scifi',\n    status: 'planning',\n    wordCount: 12000,\n    chapterCount: 8,\n    characterCount: 6,\n    progress: 25,\n    createdAt: '2024-01-10',\n    updatedAt: '2024-01-14',\n    isPreset: false\n  }, {\n    id: 3,\n    name: '都市修仙',\n    title: '现代仙侠录',\n    author: '作者C',\n    type: 'modern',\n    status: 'completed',\n    wordCount: 156000,\n    chapterCount: 78,\n    characterCount: 20,\n    progress: 100,\n    createdAt: '2023-12-01',\n    updatedAt: '2024-01-13',\n    isPreset: false\n  }, {\n    id: 4,\n    name: '【预置】玄幻世界模板',\n    title: '修仙世界设定模板',\n    author: '系统',\n    type: 'fantasy',\n    status: 'completed',\n    wordCount: 50000,\n    chapterCount: 20,\n    characterCount: 15,\n    progress: 100,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n    isPreset: true\n  }, {\n    id: 5,\n    name: '【预置】现代都市模板',\n    title: '都市异能设定模板',\n    author: '系统',\n    type: 'modern',\n    status: 'completed',\n    wordCount: 30000,\n    chapterCount: 15,\n    characterCount: 10,\n    progress: 100,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-01',\n    isPreset: true\n  }];\n  useEffect(() => {\n    loadProjects();\n  }, []);\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProjects(mockProjects);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目列表失败');\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = project => {\n    setEditingProject(project);\n    form.setFieldsValue(project);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除项目\n      await axios.delete(`/api/projects/${id}`);\n\n      // 删除成功后从列表中移除\n      setProjects(projects.filter(p => p.id !== id));\n      message.success('项目删除成功');\n\n      // 重新加载项目列表以更新统计\n      fetchProjects();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除项目失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '删除项目失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingProject) {\n        // 更新项目\n        const updatedProjects = projects.map(p => p.id === editingProject.id ? {\n          ...p,\n          ...values\n        } : p);\n        setProjects(updatedProjects);\n        message.success('项目更新成功');\n      } else {\n        // 创建项目\n        const newProject = {\n          id: Date.now(),\n          ...values,\n          wordCount: 0,\n          chapterCount: 0,\n          characterCount: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setProjects([...projects, newProject]);\n        message.success('项目创建成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n  const handleDuplicate = async project => {\n    try {\n      const newProject = {\n        ...project,\n        id: Date.now(),\n        name: `${project.name} (副本)`,\n        status: 'planning',\n        progress: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setProjects([...projects, newProject]);\n      message.success('项目复制成功');\n    } catch (error) {\n      message.error('复制项目失败');\n    }\n  };\n  const columns = [{\n    title: '项目名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [text, record.isPreset && /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          size: \"small\",\n          children: \"\\u9884\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), record.title && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '作者',\n    dataIndex: 'author',\n    key: 'author'\n  }, {\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      children: getTypeText(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: progress => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: progress,\n      size: \"small\",\n      status: progress === 100 ? 'success' : 'active'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '统计',\n    key: 'stats',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u5B57\\u6570: \", (record.wordCount / 10000).toFixed(1), \"\\u4E07\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u7AE0\\u8282: \", record.chapterCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\u4EBA\\u7269: \", record.characterCount]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后修改',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt'\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/projects/${record.id}`),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDuplicate(record),\n        children: \"\\u590D\\u5236\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u9879\\u76EE\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreate,\n            children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-right\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u5165\\u9879\\u76EE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 29\n              }, this),\n              children: \"\\u5BFC\\u51FA\\u9879\\u76EE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: projects,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个项目`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingProject ? '编辑项目' : '新建项目',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入项目名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5C0F\\u8BF4\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"author\",\n              label: \"\\u4F5C\\u8005\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F5C\\u8005\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择项目类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"fantasy\",\n                  children: \"\\u5947\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"xianxia\",\n                  children: \"\\u4ED9\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"wuxia\",\n                  children: \"\\u6B66\\u4FA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"scifi\",\n                  children: \"\\u79D1\\u5E7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"modern\",\n                  children: \"\\u73B0\\u4EE3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"historical\",\n                  children: \"\\u5386\\u53F2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"romance\",\n                  children: \"\\u8A00\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u7B80\\u4ECB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectList, \"g2W8o9nUU2hYGxU5aQ47s0oeDt4=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = ProjectList;\nexport default ProjectList;\nvar _c;\n$RefreshReg$(_c, \"ProjectList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Progress", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Typography", "Row", "Col", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "ExportOutlined", "ImportOutlined", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "ProjectList", "_s", "navigate", "projects", "setProjects", "loading", "setLoading", "modalVisible", "setModalVisible", "editingProject", "setEditingProject", "form", "useForm", "mockProjects", "id", "name", "title", "author", "type", "status", "wordCount", "chapterCount", "characterCount", "progress", "createdAt", "updatedAt", "isPreset", "loadProjects", "setTimeout", "error", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "handleCreate", "resetFields", "handleEdit", "project", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "delete", "filter", "p", "success", "fetchProjects", "_error$response", "_error$response$data", "console", "response", "data", "detail", "handleSubmit", "values", "updatedProjects", "map", "newProject", "Date", "now", "toISOString", "split", "handleDuplicate", "columns", "dataIndex", "key", "render", "text", "record", "children", "style", "fontWeight", "display", "alignItems", "gap", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "percent", "_", "toFixed", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "level", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "width", "layout", "onFinish", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "value", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Progress,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  ExportOutlined,\n  ImportOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst ProjectList = () => {\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockProjects = [\n    {\n      id: 1,\n      name: '仙侠传说',\n      title: '九天仙缘录',\n      author: '作者A',\n      type: 'xianxia',\n      status: 'writing',\n      wordCount: 89000,\n      chapterCount: 45,\n      characterCount: 12,\n      progress: 65,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-15',\n      isPreset: false\n    },\n    {\n      id: 2,\n      name: '星际征途',\n      title: '银河帝国崛起',\n      author: '作者B',\n      type: 'scifi',\n      status: 'planning',\n      wordCount: 12000,\n      chapterCount: 8,\n      characterCount: 6,\n      progress: 25,\n      createdAt: '2024-01-10',\n      updatedAt: '2024-01-14',\n      isPreset: false\n    },\n    {\n      id: 3,\n      name: '都市修仙',\n      title: '现代仙侠录',\n      author: '作者C',\n      type: 'modern',\n      status: 'completed',\n      wordCount: 156000,\n      chapterCount: 78,\n      characterCount: 20,\n      progress: 100,\n      createdAt: '2023-12-01',\n      updatedAt: '2024-01-13',\n      isPreset: false\n    },\n    {\n      id: 4,\n      name: '【预置】玄幻世界模板',\n      title: '修仙世界设定模板',\n      author: '系统',\n      type: 'fantasy',\n      status: 'completed',\n      wordCount: 50000,\n      chapterCount: 20,\n      characterCount: 15,\n      progress: 100,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n      isPreset: true\n    },\n    {\n      id: 5,\n      name: '【预置】现代都市模板',\n      title: '都市异能设定模板',\n      author: '系统',\n      type: 'modern',\n      status: 'completed',\n      wordCount: 30000,\n      chapterCount: 15,\n      characterCount: 10,\n      progress: 100,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n      isPreset: true\n    }\n  ];\n\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProjects(mockProjects);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目列表失败');\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (project) => {\n    setEditingProject(project);\n    form.setFieldsValue(project);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除项目\n      await axios.delete(`/api/projects/${id}`);\n\n      // 删除成功后从列表中移除\n      setProjects(projects.filter(p => p.id !== id));\n      message.success('项目删除成功');\n\n      // 重新加载项目列表以更新统计\n      fetchProjects();\n    } catch (error) {\n      console.error('删除项目失败:', error);\n      message.error(error.response?.data?.detail || '删除项目失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      if (editingProject) {\n        // 更新项目\n        const updatedProjects = projects.map(p =>\n          p.id === editingProject.id ? { ...p, ...values } : p\n        );\n        setProjects(updatedProjects);\n        message.success('项目更新成功');\n      } else {\n        // 创建项目\n        const newProject = {\n          id: Date.now(),\n          ...values,\n          wordCount: 0,\n          chapterCount: 0,\n          characterCount: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setProjects([...projects, newProject]);\n        message.success('项目创建成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleDuplicate = async (project) => {\n    try {\n      const newProject = {\n        ...project,\n        id: Date.now(),\n        name: `${project.name} (副本)`,\n        status: 'planning',\n        progress: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setProjects([...projects, newProject]);\n      message.success('项目复制成功');\n    } catch (error) {\n      message.error('复制项目失败');\n    }\n  };\n\n  const columns = [\n    {\n      title: '项目名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {text}\n            {record.isPreset && (\n              <Tag color=\"blue\" size=\"small\">预置</Tag>\n            )}\n          </div>\n          {record.title && (\n            <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: '作者',\n      dataIndex: 'author',\n      key: 'author',\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => <Tag>{getTypeText(type)}</Tag>,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>\n      ),\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress) => (\n        <Progress\n          percent={progress}\n          size=\"small\"\n          status={progress === 100 ? 'success' : 'active'}\n        />\n      ),\n    },\n    {\n      title: '统计',\n      key: 'stats',\n      render: (_, record) => (\n        <div style={{ fontSize: '12px' }}>\n          <div>字数: {(record.wordCount / 10000).toFixed(1)}万</div>\n          <div>章节: {record.chapterCount}</div>\n          <div>人物: {record.characterCount}</div>\n        </div>\n      ),\n    },\n    {\n      title: '最后修改',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/projects/${record.id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<CopyOutlined />}\n            onClick={() => handleDuplicate(record)}\n          >\n            复制\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个项目吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">项目管理</Title>\n      </div>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreate}\n            >\n              新建项目\n            </Button>\n          </div>\n          <div className=\"toolbar-right\">\n            <Space>\n              <Button icon={<ImportOutlined />}>导入项目</Button>\n              <Button icon={<ExportOutlined />}>导出项目</Button>\n            </Space>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={projects}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个项目`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingProject ? '编辑项目' : '新建项目'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"项目名称\"\n                rules={[{ required: true, message: '请输入项目名称' }]}\n              >\n                <Input placeholder=\"请输入项目名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"title\"\n                label=\"小说标题\"\n              >\n                <Input placeholder=\"请输入小说标题\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"author\"\n                label=\"作者\"\n              >\n                <Input placeholder=\"请输入作者名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"项目类型\"\n                rules={[{ required: true, message: '请选择项目类型' }]}\n              >\n                <Select placeholder=\"请选择项目类型\">\n                  <Option value=\"fantasy\">奇幻</Option>\n                  <Option value=\"xianxia\">仙侠</Option>\n                  <Option value=\"wuxia\">武侠</Option>\n                  <Option value=\"scifi\">科幻</Option>\n                  <Option value=\"modern\">现代</Option>\n                  <Option value=\"historical\">历史</Option>\n                  <Option value=\"romance\">言情</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"项目简介\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入项目简介\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAM,CAAC,GAAGd,UAAU;AAC5B,MAAM;EAAEe;AAAO,CAAC,GAAGlB,MAAM;AACzB,MAAM;EAAEmB;AAAS,CAAC,GAAGpB,KAAK;AAE1B,MAAMqB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0C,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE,MAAM;IACjBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,UAAU;IACjBC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,UAAU;IACjBC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE;EACZ,CAAC,CACF;EAEDxD,SAAS,CAAC,MAAM;IACdyD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BrB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAsB,UAAU,CAAC,MAAM;QACfxB,WAAW,CAACS,YAAY,CAAC;QACzBP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,UAAU,CAAC;MACzBvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,cAAc,GAAIX,MAAM,IAAK;IACjC,MAAMY,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACZ,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMkB,aAAa,GAAIlB,MAAM,IAAK;IAChC,MAAMmB,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACnB,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMoB,WAAW,GAAIrB,IAAI,IAAK;IAC5B,MAAMoB,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAACpB,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBrC,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACqC,WAAW,CAAC,CAAC;IAClBxC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyC,UAAU,GAAIC,OAAO,IAAK;IAC9BxC,iBAAiB,CAACwC,OAAO,CAAC;IAC1BvC,IAAI,CAACwC,cAAc,CAACD,OAAO,CAAC;IAC5B1C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAOtC,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAMpB,KAAK,CAAC2D,MAAM,CAAC,iBAAiBvC,EAAE,EAAE,CAAC;;MAEzC;MACAV,WAAW,CAACD,QAAQ,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC9CjC,OAAO,CAAC2E,OAAO,CAAC,QAAQ,CAAC;;MAEzB;MACAC,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA,IAAA6B,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAAC/B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhD,OAAO,CAACgD,KAAK,CAAC,EAAA6B,eAAA,GAAA7B,KAAK,CAACgC,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAI,QAAQ,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,IAAIxD,cAAc,EAAE;QAClB;QACA,MAAMyD,eAAe,GAAG/D,QAAQ,CAACgE,GAAG,CAACZ,CAAC,IACpCA,CAAC,CAACzC,EAAE,KAAKL,cAAc,CAACK,EAAE,GAAG;UAAE,GAAGyC,CAAC;UAAE,GAAGU;QAAO,CAAC,GAAGV,CACrD,CAAC;QACDnD,WAAW,CAAC8D,eAAe,CAAC;QAC5BrF,OAAO,CAAC2E,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMY,UAAU,GAAG;UACjBtD,EAAE,EAAEuD,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGL,MAAM;UACT7C,SAAS,EAAE,CAAC;UACZC,YAAY,EAAE,CAAC;UACfC,cAAc,EAAE,CAAC;UACjBC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD/C,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDpE,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEiE,UAAU,CAAC,CAAC;QACtCvF,OAAO,CAAC2E,OAAO,CAAC,QAAQ,CAAC;MAC3B;MACAhD,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAM4C,eAAe,GAAG,MAAOvB,OAAO,IAAK;IACzC,IAAI;MACF,MAAMkB,UAAU,GAAG;QACjB,GAAGlB,OAAO;QACVpC,EAAE,EAAEuD,IAAI,CAACC,GAAG,CAAC,CAAC;QACdvD,IAAI,EAAE,GAAGmC,OAAO,CAACnC,IAAI,OAAO;QAC5BI,MAAM,EAAE,UAAU;QAClBI,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjD/C,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAClD,CAAC;MACDpE,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEiE,UAAU,CAAC,CAAC;MACtCvF,OAAO,CAAC2E,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAM6C,OAAO,GAAG,CACd;IACE1D,KAAK,EAAE,MAAM;IACb2D,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBnF,OAAA;MAAAoF,QAAA,gBACEpF,OAAA;QAAKqF,KAAK,EAAE;UAAEC,UAAU,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAL,QAAA,GACnFF,IAAI,EACJC,MAAM,CAACrD,QAAQ,iBACd9B,OAAA,CAACrB,GAAG;UAAC+G,KAAK,EAAC,MAAM;UAACC,IAAI,EAAC,OAAO;UAAAP,QAAA,EAAC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACLZ,MAAM,CAAC/D,KAAK,iBACXpB,OAAA;QAAKqF,KAAK,EAAE;UAAEW,QAAQ,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,EAAED,MAAM,CAAC/D;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACrE;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACE3E,KAAK,EAAE,IAAI;IACX2D,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE;EACP,CAAC,EACD;IACE5D,KAAK,EAAE,IAAI;IACX2D,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAG3D,IAAI,iBAAKtB,OAAA,CAACrB,GAAG;MAAAyG,QAAA,EAAEzC,WAAW,CAACrB,IAAI;IAAC;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACjD,CAAC,EACD;IACE3E,KAAK,EAAE,IAAI;IACX2D,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG1D,MAAM,iBACbvB,OAAA,CAACrB,GAAG;MAAC+G,KAAK,EAAExD,cAAc,CAACX,MAAM,CAAE;MAAA6D,QAAA,EAAE3C,aAAa,CAAClB,MAAM;IAAC;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEpE,CAAC,EACD;IACE3E,KAAK,EAAE,IAAI;IACX2D,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGtD,QAAQ,iBACf3B,OAAA,CAACpB,QAAQ;MACPqH,OAAO,EAAEtE,QAAS;MAClBgE,IAAI,EAAC,OAAO;MACZpE,MAAM,EAAEI,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;IAAS;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD;EAEL,CAAC,EACD;IACE3E,KAAK,EAAE,IAAI;IACX4D,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChBnF,OAAA;MAAKqF,KAAK,EAAE;QAAEW,QAAQ,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBAC/BpF,OAAA;QAAAoF,QAAA,GAAK,gBAAI,EAAC,CAACD,MAAM,CAAC3D,SAAS,GAAG,KAAK,EAAE2E,OAAO,CAAC,CAAC,CAAC,EAAC,QAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvD/F,OAAA;QAAAoF,QAAA,GAAK,gBAAI,EAACD,MAAM,CAAC1D,YAAY;MAAA;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpC/F,OAAA;QAAAoF,QAAA,GAAK,gBAAI,EAACD,MAAM,CAACzD,cAAc;MAAA;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC;EAET,CAAC,EACD;IACE3E,KAAK,EAAE,MAAM;IACb2D,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACE5D,KAAK,EAAE,IAAI;IACX4D,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChBnF,OAAA,CAACtB,KAAK;MAAA0G,QAAA,gBACJpF,OAAA,CAACvB,MAAM;QACL6C,IAAI,EAAC,MAAM;QACX8E,IAAI,eAAEpG,OAAA,CAACP,WAAW;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBM,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAAC,aAAa6E,MAAM,CAACjE,EAAE,EAAE,CAAE;QAAAkE,QAAA,EACnD;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA,CAACvB,MAAM;QACL6C,IAAI,EAAC,MAAM;QACX8E,IAAI,eAAEpG,OAAA,CAACT,YAAY;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBM,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC8B,MAAM,CAAE;QAAAC,QAAA,EACnC;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA,CAACvB,MAAM;QACL6C,IAAI,EAAC,MAAM;QACX8E,IAAI,eAAEpG,OAAA,CAACN,YAAY;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBM,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACM,MAAM,CAAE;QAAAC,QAAA,EACxC;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/F,OAAA,CAACd,UAAU;QACTkC,KAAK,EAAC,oEAAa;QACnBkF,SAAS,EAAEA,CAAA,KAAM9C,YAAY,CAAC2B,MAAM,CAACjE,EAAE,CAAE;QACzCqF,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEfpF,OAAA,CAACvB,MAAM;UAAC6C,IAAI,EAAC,MAAM;UAACmF,MAAM;UAACL,IAAI,eAAEpG,OAAA,CAACR,cAAc;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE/F,OAAA;IAAK0G,SAAS,EAAC,SAAS;IAAAtB,QAAA,gBACtBpF,OAAA;MAAK0G,SAAS,EAAC,aAAa;MAAAtB,QAAA,eAC1BpF,OAAA,CAACC,KAAK;QAAC0G,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAtB,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAEN/F,OAAA,CAACzB,IAAI;MAAA6G,QAAA,gBACHpF,OAAA;QAAK0G,SAAS,EAAC,SAAS;QAAAtB,QAAA,gBACtBpF,OAAA;UAAK0G,SAAS,EAAC,cAAc;UAAAtB,QAAA,eAC3BpF,OAAA,CAACvB,MAAM;YACL6C,IAAI,EAAC,SAAS;YACd8E,IAAI,eAAEpG,OAAA,CAACV,YAAY;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBM,OAAO,EAAElD,YAAa;YAAAiC,QAAA,EACvB;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/F,OAAA;UAAK0G,SAAS,EAAC,eAAe;UAAAtB,QAAA,eAC5BpF,OAAA,CAACtB,KAAK;YAAA0G,QAAA,gBACJpF,OAAA,CAACvB,MAAM;cAAC2H,IAAI,eAAEpG,OAAA,CAACJ,cAAc;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAX,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C/F,OAAA,CAACvB,MAAM;cAAC2H,IAAI,eAAEpG,OAAA,CAACL,cAAc;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAX,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/F,OAAA,CAACxB,KAAK;QACJsG,OAAO,EAAEA,OAAQ;QACjB8B,UAAU,EAAErG,QAAS;QACrBsG,MAAM,EAAC,IAAI;QACXpG,OAAO,EAAEA,OAAQ;QACjBqG,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/F,OAAA,CAACnB,KAAK;MACJuC,KAAK,EAAEP,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCsG,IAAI,EAAExG,YAAa;MACnByG,QAAQ,EAAEA,CAAA,KAAMxG,eAAe,CAAC,KAAK,CAAE;MACvCyG,IAAI,EAAEA,CAAA,KAAMtG,IAAI,CAACuG,MAAM,CAAC,CAAE;MAC1BC,KAAK,EAAE,GAAI;MAAAnC,QAAA,eAEXpF,OAAA,CAAClB,IAAI;QACHiC,IAAI,EAAEA,IAAK;QACXyG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAErD,YAAa;QAAAgB,QAAA,gBAEvBpF,OAAA,CAACZ,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAAtC,QAAA,gBACdpF,OAAA,CAACX,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZpF,OAAA,CAAClB,IAAI,CAAC8I,IAAI;cACRzG,IAAI,EAAC,MAAM;cACX0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmG,QAAA,eAEhDpF,OAAA,CAACjB,KAAK;gBAACiJ,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/F,OAAA,CAACX,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZpF,OAAA,CAAClB,IAAI,CAAC8I,IAAI;cACRzG,IAAI,EAAC,OAAO;cACZ0G,KAAK,EAAC,0BAAM;cAAAzC,QAAA,eAEZpF,OAAA,CAACjB,KAAK;gBAACiJ,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/F,OAAA,CAACZ,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAAtC,QAAA,gBACdpF,OAAA,CAACX,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZpF,OAAA,CAAClB,IAAI,CAAC8I,IAAI;cACRzG,IAAI,EAAC,QAAQ;cACb0G,KAAK,EAAC,cAAI;cAAAzC,QAAA,eAEVpF,OAAA,CAACjB,KAAK;gBAACiJ,WAAW,EAAC;cAAS;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/F,OAAA,CAACX,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAAvC,QAAA,eACZpF,OAAA,CAAClB,IAAI,CAAC8I,IAAI;cACRzG,IAAI,EAAC,MAAM;cACX0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAmG,QAAA,eAEhDpF,OAAA,CAAChB,MAAM;gBAACgJ,WAAW,EAAC,4CAAS;gBAAA5C,QAAA,gBAC3BpF,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,OAAO;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,OAAO;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,YAAY;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC/F,OAAA,CAACE,MAAM;kBAAC+H,KAAK,EAAC,SAAS;kBAAA7C,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/F,OAAA,CAAClB,IAAI,CAAC8I,IAAI;UACRzG,IAAI,EAAC,SAAS;UACd0G,KAAK,EAAC,0BAAM;UAAAzC,QAAA,eAEZpF,OAAA,CAACG,QAAQ;YACP+H,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAS;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAnbID,WAAW;EAAA,QACEP,WAAW,EAKbf,IAAI,CAACkC,OAAO;AAAA;AAAAmH,EAAA,GANvB/H,WAAW;AAqbjB,eAAeA,WAAW;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}